import React from 'react';
import { motion } from 'framer-motion';
import HeroSection from '../components/HeroSection';
import SuccessSection from '../components/SuccessSection';
import BrandPartnersSection from '../components/BrandPartnersSection';
import BestSellingSection from '../components/BestSellingSection';
import CategoriesSection from '../components/CategoriesSection';
import ReviewsSection from '../components/ReviewsSection';
import SectionTransition from '../components/SectionTransition';
import AnimatedBackground from '../components/AnimatedBackground';

const HomePage = () => {
  return (
    <div className="relative">
      {/* Hero Section */}
      <HeroSection />

      {/* Brand Partners Section - Moved directly under hero */}
      <SectionTransition direction="up" delay={0.1}>
        <BrandPartnersSection />
      </SectionTransition>

      {/* Section Divider with Animation */}
      <motion.div
        className="h-32 relative overflow-hidden"
        style={{ backgroundColor: '#2a2a2a' }}
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 1 }}
      >
        <AnimatedBackground variant="waves" intensity="low" color="#ef4444" />
        <div className="absolute inset-0 flex items-center justify-center">
          <motion.div
            className="w-24 h-1 rounded-full"
            style={{ backgroundColor: '#ef4444' }}
            initial={{ width: 0 }}
            whileInView={{ width: 96 }}
            transition={{ duration: 1.5, ease: "easeInOut" }}
          />
        </div>
      </motion.div>

      {/* Success Section with Transition */}
      <SectionTransition direction="up" delay={0.2}>
        <SuccessSection />
      </SectionTransition>

      {/* Section Divider */}
      <motion.div
        className="h-20 relative"
        style={{ backgroundColor: '#1a1a1a' }}
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <AnimatedBackground variant="geometric" intensity="low" color="#ef4444" />
      </motion.div>

      {/* Best Selling Section with Transition */}
      <SectionTransition direction="right" delay={0.3}>
        <BestSellingSection />
      </SectionTransition>

      {/* Categories Section with Transition */}
      <SectionTransition direction="left" delay={0.2}>
        <CategoriesSection />
      </SectionTransition>

      {/* Section Divider */}
      <motion.div
        className="h-24 relative overflow-hidden"
        style={{ backgroundColor: '#2a2a2a' }}
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 1 }}
      >
        <AnimatedBackground variant="gradient" intensity="medium" color="#ef4444" />
        <div className="absolute inset-0 flex items-center justify-center">
          <motion.div
            className="flex space-x-2"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.5 }}
          >
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: '#ef4444' }}
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.3
                }}
              />
            ))}
          </motion.div>
        </div>
      </motion.div>

      {/* Reviews Section with Transition */}
      <SectionTransition direction="rotate" delay={0.4}>
        <ReviewsSection />
      </SectionTransition>
    </div>
  );
};

export default HomePage;
