import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Home, 
  ShoppingBag, 
  Users, 
  BarChart3, 
  Settings, 
  LogOut, 
  Menu, 
  X,
  Package,
  Heart,
  CreditCard,
  Bell,
  User,
  Store
} from 'lucide-react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuthState } from '../hooks/useAuth';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { authState, logout } = useAuthState();

  const handleLogout = async () => {
    await logout();
    navigate('/');
  };

  const getMenuItems = () => {
    const baseItems = [
      { icon: Home, label: 'Dashboard', path: '/dashboard' },
      { icon: User, label: 'Profile', path: '/dashboard/profile' },
      { icon: Settings, label: 'Settings', path: '/dashboard/settings' }
    ];

    switch (authState.user?.accountType) {
      case 'admin':
        return [
          ...baseItems,
          { icon: Users, label: 'Users', path: '/dashboard/users' },
          { icon: Package, label: 'Products', path: '/dashboard/products' },
          { icon: ShoppingBag, label: 'Orders', path: '/dashboard/orders' },
          { icon: Store, label: 'Sellers', path: '/dashboard/sellers' },
          { icon: BarChart3, label: 'Analytics', path: '/dashboard/analytics' }
        ];
      case 'seller':
        return [
          ...baseItems,
          { icon: Package, label: 'My Products', path: '/dashboard/my-products' },
          { icon: ShoppingBag, label: 'Orders', path: '/dashboard/orders' },
          { icon: BarChart3, label: 'Sales Analytics', path: '/dashboard/sales' },
          { icon: Store, label: 'Store Settings', path: '/dashboard/store' }
        ];
      case 'customer':
      default:
        return [
          ...baseItems,
          { icon: ShoppingBag, label: 'My Orders', path: '/dashboard/orders' },
          { icon: Heart, label: 'Wishlist', path: '/dashboard/wishlist' },
          { icon: CreditCard, label: 'Payment Methods', path: '/dashboard/payments' },
          { icon: Bell, label: 'Notifications', path: '/dashboard/notifications' }
        ];
    }
  };

  const menuItems = getMenuItems();

  const sidebarVariants = {
    open: { x: 0 },
    closed: { x: '-100%' }
  };

  return (
    <div className="min-h-screen flex" style={{ backgroundColor: '#2a2a2a' }}>
      {/* Mobile Overlay */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            className="fixed inset-0 z-40 lg:hidden"
            style={{ backgroundColor: 'rgba(42, 42, 42, 0.8)' }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.div
        className="fixed inset-y-0 left-0 z-50 w-64 lg:relative lg:translate-x-0"
        style={{ backgroundColor: '#1a1a1a' }}
        variants={sidebarVariants}
        initial="closed"
        animate={sidebarOpen ? "open" : "closed"}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b" style={{ borderColor: '#4a4a4a' }}>
            <Link to="/" className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#ef4444' }}>
                <span className="text-white font-bold">D</span>
              </div>
              <span className="text-xl font-bold" style={{ color: '#f8fafc' }}>DANFU</span>
            </Link>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden p-2 rounded-lg transition-colors"
              style={{ color: '#94a3b8' }}
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* User Info */}
          <div className="p-6 border-b" style={{ borderColor: '#4a4a4a' }}>
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-full flex items-center justify-center" style={{ backgroundColor: '#ef4444' }}>
                <User className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="font-medium" style={{ color: '#f8fafc' }}>
                  {authState.user?.firstName} {authState.user?.lastName}
                </h3>
                <p className="text-sm capitalize" style={{ color: '#94a3b8' }}>
                  {authState.user?.accountType}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-6">
            <ul className="space-y-2">
              {menuItems.map((item) => {
                const Icon = item.icon;
                const isActive = location.pathname === item.path;
                
                return (
                  <li key={item.path}>
                    <Link
                      to={item.path}
                      className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                        isActive ? 'shadow-lg' : ''
                      }`}
                      style={{
                        backgroundColor: isActive ? '#ef4444' : 'transparent',
                        color: isActive ? '#ffffff' : '#94a3b8'
                      }}
                      onMouseEnter={(e) => {
                        if (!isActive) {
                          e.currentTarget.style.backgroundColor = '#2a2a2a';
                          e.currentTarget.style.color = '#f8fafc';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (!isActive) {
                          e.currentTarget.style.backgroundColor = 'transparent';
                          e.currentTarget.style.color = '#94a3b8';
                        }
                      }}
                      onClick={() => setSidebarOpen(false)}
                    >
                      <Icon className="h-5 w-5" />
                      <span className="font-medium">{item.label}</span>
                    </Link>
                  </li>
                );
              })}
            </ul>
          </nav>

          {/* Logout */}
          <div className="p-6 border-t" style={{ borderColor: '#4a4a4a' }}>
            <button
              onClick={handleLogout}
              className="flex items-center gap-3 w-full px-4 py-3 rounded-lg transition-colors"
              style={{ color: '#94a3b8' }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#ef4444';
                e.currentTarget.style.color = '#ffffff';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = '#94a3b8';
              }}
            >
              <LogOut className="h-5 w-5" />
              <span className="font-medium">Logout</span>
            </button>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Top Bar */}
        <header className="border-b p-4 lg:p-6" style={{ backgroundColor: '#1a1a1a', borderColor: '#4a4a4a' }}>
          <div className="flex items-center justify-between">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden p-2 rounded-lg transition-colors"
              style={{ color: '#94a3b8' }}
            >
              <Menu className="h-6 w-6" />
            </button>
            
            <div className="flex items-center gap-4">
              <h1 className="text-2xl font-bold" style={{ color: '#f8fafc' }}>
                Dashboard
              </h1>
            </div>

            <div className="flex items-center gap-4">
              <button className="p-2 rounded-lg transition-colors relative" style={{ color: '#94a3b8' }}>
                <Bell className="h-6 w-6" />
                <span className="absolute -top-1 -right-1 w-3 h-3 rounded-full" style={{ backgroundColor: '#ef4444' }}></span>
              </button>
              <Link to="/" className="text-sm transition-colors" style={{ color: '#94a3b8' }}>
                Back to Store
              </Link>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 p-4 lg:p-6 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
