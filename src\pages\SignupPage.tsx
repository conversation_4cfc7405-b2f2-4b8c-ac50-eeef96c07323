import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Eye, 
  EyeOff, 
  Mail, 
  Lock, 
  User, 
  Phone, 
  Calendar,
  Building,
  ArrowRight, 
  Loader2, 
  CheckCircle, 
  AlertCircle,
  UserPlus,
  Store
} from 'lucide-react';
import { useAuthState } from '../hooks/useAuth';
import AnimatedBackground from '../components/AnimatedBackground';

const SignupPage = () => {
  const navigate = useNavigate();
  const { signup, authState } = useAuthState();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [accountType, setAccountType] = useState<'customer' | 'seller'>('customer');
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phoneNumber: '',
    dateOfBirth: '',
    acceptTerms: false,
    subscribeNewsletter: false,
    // Seller specific fields
    businessName: '',
    businessType: '',
    taxId: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showSuccess, setShowSuccess] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    setFormData(prev => ({ 
      ...prev, 
      [name]: type === 'checkbox' ? checked : value 
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateStep = (step: number) => {
    const newErrors: Record<string, string> = {};
    
    if (step === 1) {
      if (!formData.firstName) newErrors.firstName = 'First name is required';
      if (!formData.lastName) newErrors.lastName = 'Last name is required';
      if (!formData.email) {
        newErrors.email = 'Email is required';
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = 'Please enter a valid email';
      }
    }
    
    if (step === 2) {
      if (!formData.password) {
        newErrors.password = 'Password is required';
      } else if (formData.password.length < 6) {
        newErrors.password = 'Password must be at least 6 characters';
      }
      
      if (!formData.confirmPassword) {
        newErrors.confirmPassword = 'Please confirm your password';
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }
    
    if (step === 3 && accountType === 'seller') {
      if (!formData.businessName) newErrors.businessName = 'Business name is required';
      if (!formData.businessType) newErrors.businessType = 'Business type is required';
    }
    
    if (step === 3) {
      if (!formData.acceptTerms) {
        newErrors.acceptTerms = 'You must accept the terms and conditions';
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => prev - 1);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateStep(currentStep)) return;
    
    const signupData = {
      ...formData,
      accountType
    };
    
    const result = await signup(signupData);
    
    if (result.success) {
      setShowSuccess(true);
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);
    }
  };

  const stepVariants = {
    hidden: { opacity: 0, x: 50 },
    visible: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -50 }
  };

  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden" style={{ backgroundColor: '#2a2a2a' }}>
      <AnimatedBackground variant="geometric" intensity="medium" color="#ef4444" />
      
      {/* Success Animation */}
      <AnimatePresence>
        {showSuccess && (
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center"
            style={{ backgroundColor: 'rgba(42, 42, 42, 0.9)' }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="text-center"
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ type: "spring", stiffness: 200, damping: 20 }}
            >
              <motion.div
                className="w-24 h-24 mx-auto mb-4 rounded-full flex items-center justify-center"
                style={{ backgroundColor: '#ef4444' }}
                animate={{ 
                  boxShadow: [
                    '0 0 0 0 rgba(239, 68, 68, 0.7)',
                    '0 0 0 20px rgba(239, 68, 68, 0)',
                    '0 0 0 0 rgba(239, 68, 68, 0)'
                  ]
                }}
                transition={{ duration: 1.5, repeat: Infinity }}
              >
                <CheckCircle className="h-12 w-12 text-white" />
              </motion.div>
              <motion.h2
                className="text-2xl font-bold mb-2"
                style={{ color: '#f8fafc' }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                Welcome to DANFU!
              </motion.h2>
              <motion.p
                style={{ color: '#94a3b8' }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                Account created successfully. Redirecting to dashboard...
              </motion.p>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      <div className="max-w-lg w-full mx-4 relative z-10">
        <motion.div
          className="rounded-2xl p-8 border backdrop-blur-sm"
          style={{ 
            backgroundColor: 'rgba(26, 26, 26, 0.8)', 
            borderColor: '#4a4a4a'
          }}
          initial={{ opacity: 0, y: 50, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.8, ease: [0.25, 0.25, 0.25, 0.75] }}
        >
          {/* Header */}
          <motion.div
            className="text-center mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <motion.h1
              className="text-3xl font-bold mb-2"
              style={{ color: '#f8fafc' }}
              whileHover={{ scale: 1.05 }}
            >
              Join DANFU
            </motion.h1>
            <p style={{ color: '#94a3b8' }}>
              Create your account and start your journey
            </p>
          </motion.div>

          {/* Account Type Selection */}
          <motion.div
            className="mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <div className="grid grid-cols-2 gap-4">
              <motion.button
                onClick={() => setAccountType('customer')}
                className={`p-4 rounded-lg border transition-all duration-300 ${
                  accountType === 'customer' ? 'border-red-500 bg-red-500/10' : 'border-gray-600'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <UserPlus className="h-6 w-6 mx-auto mb-2" style={{ color: accountType === 'customer' ? '#ef4444' : '#94a3b8' }} />
                <span className="text-sm font-medium" style={{ color: accountType === 'customer' ? '#ef4444' : '#94a3b8' }}>
                  Customer
                </span>
              </motion.button>
              
              <motion.button
                onClick={() => setAccountType('seller')}
                className={`p-4 rounded-lg border transition-all duration-300 ${
                  accountType === 'seller' ? 'border-red-500 bg-red-500/10' : 'border-gray-600'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Store className="h-6 w-6 mx-auto mb-2" style={{ color: accountType === 'seller' ? '#ef4444' : '#94a3b8' }} />
                <span className="text-sm font-medium" style={{ color: accountType === 'seller' ? '#ef4444' : '#94a3b8' }}>
                  Seller
                </span>
              </motion.button>
            </div>
          </motion.div>

          {/* Progress Indicator */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              {[1, 2, 3].map((step) => (
                <div
                  key={step}
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 ${
                    step <= currentStep 
                      ? 'bg-red-500 text-white' 
                      : 'bg-gray-600 text-gray-400'
                  }`}
                >
                  {step}
                </div>
              ))}
            </div>
            <div className="w-full bg-gray-600 rounded-full h-2">
              <motion.div
                className="h-2 rounded-full"
                style={{ backgroundColor: '#ef4444' }}
                initial={{ width: '33%' }}
                animate={{ width: `${(currentStep / 3) * 100}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </div>

          {/* Form Steps */}
          <form onSubmit={handleSubmit}>
            <AnimatePresence mode="wait">
              {/* Step 1: Basic Info */}
              {currentStep === 1 && (
                <motion.div
                  key="step1"
                  variants={stepVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  className="space-y-4"
                >
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: '#f8fafc' }}>
                        First Name
                      </label>
                      <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5" style={{ color: '#94a3b8' }} />
                        <input
                          type="text"
                          name="firstName"
                          value={formData.firstName}
                          onChange={handleInputChange}
                          className="w-full pl-10 pr-4 py-3 rounded-lg border transition-all duration-300 focus:outline-none"
                          style={{ 
                            backgroundColor: '#1a1a1a', 
                            borderColor: errors.firstName ? '#ef4444' : '#4a4a4a',
                            color: '#f8fafc'
                          }}
                          placeholder="John"
                        />
                      </div>
                      {errors.firstName && (
                        <p className="text-sm mt-1" style={{ color: '#ef4444' }}>{errors.firstName}</p>
                      )}
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: '#f8fafc' }}>
                        Last Name
                      </label>
                      <input
                        type="text"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg border transition-all duration-300 focus:outline-none"
                        style={{ 
                          backgroundColor: '#1a1a1a', 
                          borderColor: errors.lastName ? '#ef4444' : '#4a4a4a',
                          color: '#f8fafc'
                        }}
                        placeholder="Doe"
                      />
                      {errors.lastName && (
                        <p className="text-sm mt-1" style={{ color: '#ef4444' }}>{errors.lastName}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2" style={{ color: '#f8fafc' }}>
                      Email Address
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5" style={{ color: '#94a3b8' }} />
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="w-full pl-10 pr-4 py-3 rounded-lg border transition-all duration-300 focus:outline-none"
                        style={{ 
                          backgroundColor: '#1a1a1a', 
                          borderColor: errors.email ? '#ef4444' : '#4a4a4a',
                          color: '#f8fafc'
                        }}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    {errors.email && (
                      <p className="text-sm mt-1" style={{ color: '#ef4444' }}>{errors.email}</p>
                    )}
                  </div>

                  <motion.button
                    type="button"
                    onClick={nextStep}
                    className="w-full py-3 rounded-lg font-semibold flex items-center justify-center gap-2 transition-all duration-300"
                    style={{ backgroundColor: '#ef4444', color: '#ffffff' }}
                    whileHover={{ scale: 1.02, backgroundColor: '#dc2626' }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Continue
                    <ArrowRight className="h-5 w-5" />
                  </motion.button>
                </motion.div>
              )}

              {/* Step 2: Password */}
              {currentStep === 2 && (
                <motion.div
                  key="step2"
                  variants={stepVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  className="space-y-4"
                >
                  <div>
                    <label className="block text-sm font-medium mb-2" style={{ color: '#f8fafc' }}>
                      Password
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5" style={{ color: '#94a3b8' }} />
                      <input
                        type={showPassword ? 'text' : 'password'}
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        className="w-full pl-10 pr-12 py-3 rounded-lg border transition-all duration-300 focus:outline-none"
                        style={{
                          backgroundColor: '#1a1a1a',
                          borderColor: errors.password ? '#ef4444' : '#4a4a4a',
                          color: '#f8fafc'
                        }}
                        placeholder="Enter your password"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 transition-colors"
                        style={{ color: '#94a3b8' }}
                      >
                        {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                      </button>
                    </div>
                    {errors.password && (
                      <p className="text-sm mt-1" style={{ color: '#ef4444' }}>{errors.password}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2" style={{ color: '#f8fafc' }}>
                      Confirm Password
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5" style={{ color: '#94a3b8' }} />
                      <input
                        type={showConfirmPassword ? 'text' : 'password'}
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        className="w-full pl-10 pr-12 py-3 rounded-lg border transition-all duration-300 focus:outline-none"
                        style={{
                          backgroundColor: '#1a1a1a',
                          borderColor: errors.confirmPassword ? '#ef4444' : '#4a4a4a',
                          color: '#f8fafc'
                        }}
                        placeholder="Confirm your password"
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 transition-colors"
                        style={{ color: '#94a3b8' }}
                      >
                        {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                      </button>
                    </div>
                    {errors.confirmPassword && (
                      <p className="text-sm mt-1" style={{ color: '#ef4444' }}>{errors.confirmPassword}</p>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: '#f8fafc' }}>
                        Phone Number
                      </label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5" style={{ color: '#94a3b8' }} />
                        <input
                          type="tel"
                          name="phoneNumber"
                          value={formData.phoneNumber}
                          onChange={handleInputChange}
                          className="w-full pl-10 pr-4 py-3 rounded-lg border transition-all duration-300 focus:outline-none"
                          style={{
                            backgroundColor: '#1a1a1a',
                            borderColor: '#4a4a4a',
                            color: '#f8fafc'
                          }}
                          placeholder="+1234567890"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: '#f8fafc' }}>
                        Date of Birth
                      </label>
                      <div className="relative">
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5" style={{ color: '#94a3b8' }} />
                        <input
                          type="date"
                          name="dateOfBirth"
                          value={formData.dateOfBirth}
                          onChange={handleInputChange}
                          className="w-full pl-10 pr-4 py-3 rounded-lg border transition-all duration-300 focus:outline-none"
                          style={{
                            backgroundColor: '#1a1a1a',
                            borderColor: '#4a4a4a',
                            color: '#f8fafc'
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <motion.button
                      type="button"
                      onClick={prevStep}
                      className="flex-1 py-3 rounded-lg font-semibold border transition-all duration-300"
                      style={{ borderColor: '#4a4a4a', color: '#94a3b8' }}
                      whileHover={{ borderColor: '#ef4444', color: '#f8fafc' }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Back
                    </motion.button>
                    <motion.button
                      type="button"
                      onClick={nextStep}
                      className="flex-1 py-3 rounded-lg font-semibold flex items-center justify-center gap-2 transition-all duration-300"
                      style={{ backgroundColor: '#ef4444', color: '#ffffff' }}
                      whileHover={{ scale: 1.02, backgroundColor: '#dc2626' }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Continue
                      <ArrowRight className="h-5 w-5" />
                    </motion.button>
                  </div>
                </motion.div>
              )}

              {/* Step 3: Final Details */}
              {currentStep === 3 && (
                <motion.div
                  key="step3"
                  variants={stepVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  className="space-y-4"
                >
                  {accountType === 'seller' && (
                    <>
                      <div>
                        <label className="block text-sm font-medium mb-2" style={{ color: '#f8fafc' }}>
                          Business Name
                        </label>
                        <div className="relative">
                          <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5" style={{ color: '#94a3b8' }} />
                          <input
                            type="text"
                            name="businessName"
                            value={formData.businessName}
                            onChange={handleInputChange}
                            className="w-full pl-10 pr-4 py-3 rounded-lg border transition-all duration-300 focus:outline-none"
                            style={{
                              backgroundColor: '#1a1a1a',
                              borderColor: errors.businessName ? '#ef4444' : '#4a4a4a',
                              color: '#f8fafc'
                            }}
                            placeholder="Your Business Name"
                          />
                        </div>
                        {errors.businessName && (
                          <p className="text-sm mt-1" style={{ color: '#ef4444' }}>{errors.businessName}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-2" style={{ color: '#f8fafc' }}>
                          Business Type
                        </label>
                        <select
                          name="businessType"
                          value={formData.businessType}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 rounded-lg border transition-all duration-300 focus:outline-none"
                          style={{
                            backgroundColor: '#1a1a1a',
                            borderColor: errors.businessType ? '#ef4444' : '#4a4a4a',
                            color: '#f8fafc'
                          }}
                        >
                          <option value="">Select Business Type</option>
                          <option value="retail">Retail</option>
                          <option value="wholesale">Wholesale</option>
                          <option value="manufacturer">Manufacturer</option>
                          <option value="individual">Individual Seller</option>
                        </select>
                        {errors.businessType && (
                          <p className="text-sm mt-1" style={{ color: '#ef4444' }}>{errors.businessType}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-2" style={{ color: '#f8fafc' }}>
                          Tax ID (Optional)
                        </label>
                        <input
                          type="text"
                          name="taxId"
                          value={formData.taxId}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 rounded-lg border transition-all duration-300 focus:outline-none"
                          style={{
                            backgroundColor: '#1a1a1a',
                            borderColor: '#4a4a4a',
                            color: '#f8fafc'
                          }}
                          placeholder="Tax ID Number"
                        />
                      </div>
                    </>
                  )}

                  <div className="space-y-3">
                    <label className="flex items-start gap-3">
                      <input
                        type="checkbox"
                        name="acceptTerms"
                        checked={formData.acceptTerms}
                        onChange={handleInputChange}
                        className="mt-1"
                      />
                      <span className="text-sm" style={{ color: '#94a3b8' }}>
                        I accept the{' '}
                        <Link to="/terms" className="underline" style={{ color: '#ef4444' }}>
                          Terms and Conditions
                        </Link>{' '}
                        and{' '}
                        <Link to="/privacy" className="underline" style={{ color: '#ef4444' }}>
                          Privacy Policy
                        </Link>
                      </span>
                    </label>
                    {errors.acceptTerms && (
                      <p className="text-sm" style={{ color: '#ef4444' }}>{errors.acceptTerms}</p>
                    )}

                    <label className="flex items-start gap-3">
                      <input
                        type="checkbox"
                        name="subscribeNewsletter"
                        checked={formData.subscribeNewsletter}
                        onChange={handleInputChange}
                        className="mt-1"
                      />
                      <span className="text-sm" style={{ color: '#94a3b8' }}>
                        Subscribe to our newsletter for updates and exclusive offers
                      </span>
                    </label>
                  </div>

                  <div className="flex gap-4">
                    <motion.button
                      type="button"
                      onClick={prevStep}
                      className="flex-1 py-3 rounded-lg font-semibold border transition-all duration-300"
                      style={{ borderColor: '#4a4a4a', color: '#94a3b8' }}
                      whileHover={{ borderColor: '#ef4444', color: '#f8fafc' }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Back
                    </motion.button>
                    <motion.button
                      type="submit"
                      disabled={authState.loading}
                      className="flex-1 py-3 rounded-lg font-semibold flex items-center justify-center gap-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                      style={{ backgroundColor: '#ef4444', color: '#ffffff' }}
                      whileHover={{ scale: authState.loading ? 1 : 1.02, backgroundColor: '#dc2626' }}
                      whileTap={{ scale: authState.loading ? 1 : 0.98 }}
                    >
                      {authState.loading ? (
                        <Loader2 className="h-5 w-5 animate-spin" />
                      ) : (
                        <>
                          Create Account
                          <CheckCircle className="h-5 w-5" />
                        </>
                      )}
                    </motion.button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </form>

          {/* Footer */}
          <motion.div
            className="mt-8 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.7 }}
          >
            <p style={{ color: '#94a3b8' }}>
              Already have an account?{' '}
              <Link
                to="/login"
                className="font-medium transition-colors"
                style={{ color: '#ef4444' }}
                onMouseEnter={(e) => e.target.style.color = '#dc2626'}
                onMouseLeave={(e) => e.target.style.color = '#ef4444'}
              >
                Sign in here
              </Link>
            </p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default SignupPage;
