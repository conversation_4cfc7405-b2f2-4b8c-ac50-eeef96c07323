import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

interface AnimatedBackgroundProps {
  variant?: 'particles' | 'waves' | 'geometric' | 'gradient';
  intensity?: 'low' | 'medium' | 'high';
  color?: string;
}

const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({
  variant = 'particles',
  intensity = 'medium',
  color = '#ef4444'
}) => {
  const [elements, setElements] = useState<any[]>([]);

  const intensityMap = {
    low: 15,
    medium: 30,
    high: 50
  };

  useEffect(() => {
    const count = intensityMap[intensity];
    const newElements = Array.from({ length: count }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 4 + 1,
      delay: Math.random() * 5,
      duration: Math.random() * 10 + 5,
      direction: Math.random() > 0.5 ? 1 : -1
    }));
    setElements(newElements);
  }, [intensity]);

  const renderParticles = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {elements.map((element) => (
        <motion.div
          key={element.id}
          className="absolute rounded-full opacity-20"
          style={{
            backgroundColor: color,
            width: `${element.size}px`,
            height: `${element.size}px`,
            left: `${element.x}%`,
            top: `${element.y}%`,
            filter: 'blur(1px)'
          }}
          animate={{
            y: [0, element.direction * 100, 0],
            x: [0, element.direction * 50, 0],
            opacity: [0, 0.6, 0],
            scale: [0.5, 1.2, 0.5]
          }}
          transition={{
            duration: element.duration,
            delay: element.delay,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );

  const renderWaves = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {[...Array(3)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute inset-0"
          style={{
            background: `radial-gradient(circle at 50% 50%, ${color}20 0%, transparent 70%)`,
            transform: `scale(${1 + i * 0.3})`
          }}
          animate={{
            scale: [1 + i * 0.3, 1.5 + i * 0.3, 1 + i * 0.3],
            opacity: [0.1, 0.3, 0.1]
          }}
          transition={{
            duration: 8 + i * 2,
            repeat: Infinity,
            ease: "easeInOut",
            delay: i * 2
          }}
        />
      ))}
    </div>
  );

  const renderGeometric = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {elements.slice(0, 10).map((element, i) => (
        <motion.div
          key={element.id}
          className="absolute border opacity-10"
          style={{
            borderColor: color,
            width: `${element.size * 20}px`,
            height: `${element.size * 20}px`,
            left: `${element.x}%`,
            top: `${element.y}%`,
            borderWidth: '1px'
          }}
          animate={{
            rotate: [0, 360],
            scale: [0.8, 1.2, 0.8],
            opacity: [0.05, 0.2, 0.05]
          }}
          transition={{
            duration: element.duration,
            delay: element.delay,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      ))}
    </div>
  );

  const renderGradient = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <motion.div
        className="absolute inset-0"
        style={{
          background: `linear-gradient(45deg, ${color}10, transparent, ${color}05, transparent, ${color}10)`
        }}
        animate={{
          backgroundPosition: ['0% 0%', '100% 100%', '0% 0%']
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "linear"
        }}
      />
      <motion.div
        className="absolute inset-0"
        style={{
          background: `radial-gradient(circle at 30% 70%, ${color}15 0%, transparent 50%)`
        }}
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.6, 0.3]
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </div>
  );

  const renderVariant = () => {
    switch (variant) {
      case 'particles':
        return renderParticles();
      case 'waves':
        return renderWaves();
      case 'geometric':
        return renderGeometric();
      case 'gradient':
        return renderGradient();
      default:
        return renderParticles();
    }
  };

  return (
    <div className="absolute inset-0 z-0">
      {renderVariant()}
    </div>
  );
};

export default AnimatedBackground;
