import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { User, ShoppingCart, Heart, Menu, X } from 'lucide-react';
import { useCart } from '../context/CartContext';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();
  const { cart, toggleCart } = useCart();

  const isActive = (path: string) => location.pathname === path;

  return (
    <nav className="text-white sticky top-0 z-50 border-b" style={{ backgroundColor: '#1a1a1a', borderColor: '#4a4a4a' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link to="/" className="text-2xl font-bold transition-colors" style={{ color: '#ef4444' }} onMouseEnter={(e) => e.target.style.color = '#dc2626'} onMouseLeave={(e) => e.target.style.color = '#ef4444'}>
              DANFU
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-8">
              <Link
                to="/shop"
                className="transition-colors"
                style={{ color: isActive('/shop') ? '#ef4444' : '#f8fafc' }}
                onMouseEnter={(e) => e.target.style.color = '#ef4444'}
                onMouseLeave={(e) => e.target.style.color = isActive('/shop') ? '#ef4444' : '#f8fafc'}
              >
                Shop
              </Link>
              <Link
                to="/about"
                className="transition-colors"
                style={{ color: isActive('/about') ? '#ef4444' : '#f8fafc' }}
                onMouseEnter={(e) => e.target.style.color = '#ef4444'}
                onMouseLeave={(e) => e.target.style.color = isActive('/about') ? '#ef4444' : '#f8fafc'}
              >
                About Us
              </Link>
              <Link
                to="/contact"
                className="transition-colors"
                style={{ color: isActive('/contact') ? '#ef4444' : '#f8fafc' }}
                onMouseEnter={(e) => e.target.style.color = '#ef4444'}
                onMouseLeave={(e) => e.target.style.color = isActive('/contact') ? '#ef4444' : '#f8fafc'}
              >
                Contact
              </Link>
            </div>
          </div>



          {/* Icons */}
          <div className="hidden md:flex items-center space-x-4">
            <Link to="/login">
              <User className="h-6 w-6 cursor-pointer transition-colors" style={{ color: '#f8fafc' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#f8fafc'} />
            </Link>
            <Heart className="h-6 w-6 cursor-pointer transition-colors" style={{ color: '#f8fafc' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#f8fafc'} />
            <button onClick={toggleCart} className="relative">
              <ShoppingCart className="h-6 w-6 cursor-pointer transition-colors" style={{ color: '#f8fafc' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#f8fafc'} />
              {cart.totalItems > 0 && (
                <span className="absolute -top-2 -right-2 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse" style={{ backgroundColor: '#ef4444' }}>
                  {cart.totalItems > 99 ? '99+' : cart.totalItems}
                </span>
              )}
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="transition-colors"
              style={{ color: '#f8fafc' }}
              onMouseEnter={(e) => e.target.style.color = '#ef4444'}
              onMouseLeave={(e) => e.target.style.color = '#f8fafc'}
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden border-t" style={{ backgroundColor: '#2a2a2a', borderColor: '#4a4a4a' }}>
          <div className="px-2 pt-2 pb-3 space-y-1">
            <Link
              to="/shop"
              className="block px-3 py-2 transition-colors"
              style={{ color: isActive('/shop') ? '#ef4444' : '#f8fafc' }}
              onMouseEnter={(e) => e.target.style.color = '#ef4444'}
              onMouseLeave={(e) => e.target.style.color = isActive('/shop') ? '#ef4444' : '#f8fafc'}
              onClick={() => setIsMenuOpen(false)}
            >
              Shop
            </Link>
            <Link
              to="/about"
              className="block px-3 py-2 transition-colors"
              style={{ color: isActive('/about') ? '#ef4444' : '#f8fafc' }}
              onMouseEnter={(e) => e.target.style.color = '#ef4444'}
              onMouseLeave={(e) => e.target.style.color = isActive('/about') ? '#ef4444' : '#f8fafc'}
              onClick={() => setIsMenuOpen(false)}
            >
              About Us
            </Link>
            <Link
              to="/contact"
              className="block px-3 py-2 transition-colors"
              style={{ color: isActive('/contact') ? '#ef4444' : '#f8fafc' }}
              onMouseEnter={(e) => e.target.style.color = '#ef4444'}
              onMouseLeave={(e) => e.target.style.color = isActive('/contact') ? '#ef4444' : '#f8fafc'}
              onClick={() => setIsMenuOpen(false)}
            >
              Contact
            </Link>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;