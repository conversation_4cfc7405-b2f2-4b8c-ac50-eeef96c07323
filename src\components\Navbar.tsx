import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { User, ShoppingCart, Heart, Menu, X } from 'lucide-react';
import { useCart } from '../context/CartContext';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();
  const { cart, toggleCart } = useCart();

  const isActive = (path: string) => location.pathname === path;

  return (
    <nav className="bg-black text-white sticky top-0 z-50 border-b border-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link to="/" className="text-2xl font-bold text-red-500 hover:text-red-400 transition-colors duration-300">
              DANFU
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-center space-x-8">
              <Link
                to="/shop"
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                  isActive('/shop')
                    ? 'bg-red-500 text-white'
                    : 'text-gray-300 hover:text-white hover:bg-gray-800'
                }`}
              >
                Shop
              </Link>
              <Link
                to="/about"
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                  isActive('/about')
                    ? 'bg-red-500 text-white'
                    : 'text-gray-300 hover:text-white hover:bg-gray-800'
                }`}
              >
                About Us
              </Link>
              <Link
                to="/contact"
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                  isActive('/contact')
                    ? 'bg-red-500 text-white'
                    : 'text-gray-300 hover:text-white hover:bg-gray-800'
                }`}
              >
                Contact
              </Link>
            </div>
          </div>



          {/* Icons */}
          <div className="hidden md:flex items-center space-x-3">
            <Link to="/login" className="p-2 rounded-lg text-gray-300 hover:text-white hover:bg-gray-800 transition-all duration-300">
              <User className="h-6 w-6" />
            </Link>
            <button className="p-2 rounded-lg text-gray-300 hover:text-white hover:bg-gray-800 transition-all duration-300">
              <Heart className="h-6 w-6" />
            </button>
            <button onClick={toggleCart} className="relative p-2 rounded-lg text-gray-300 hover:text-white hover:bg-gray-800 transition-all duration-300">
              <ShoppingCart className="h-6 w-6" />
              {cart.totalItems > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold shadow-lg animate-pulse">
                  {cart.totalItems > 99 ? '99+' : cart.totalItems}
                </span>
              )}
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 rounded-lg text-gray-300 hover:text-white hover:bg-gray-800 transition-all duration-300"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden border-t border-gray-800 bg-gray-900">
          <div className="px-4 pt-4 pb-6 space-y-2">
            <Link
              to="/shop"
              className={`block px-4 py-3 rounded-lg font-medium transition-all duration-300 ${
                isActive('/shop')
                  ? 'bg-red-500 text-white'
                  : 'text-gray-300 hover:text-white hover:bg-gray-800'
              }`}
              onClick={() => setIsMenuOpen(false)}
            >
              Shop
            </Link>
            <Link
              to="/about"
              className={`block px-4 py-3 rounded-lg font-medium transition-all duration-300 ${
                isActive('/about')
                  ? 'bg-red-500 text-white'
                  : 'text-gray-300 hover:text-white hover:bg-gray-800'
              }`}
              onClick={() => setIsMenuOpen(false)}
            >
              About Us
            </Link>
            <Link
              to="/contact"
              className={`block px-4 py-3 rounded-lg font-medium transition-all duration-300 ${
                isActive('/contact')
                  ? 'bg-red-500 text-white'
                  : 'text-gray-300 hover:text-white hover:bg-gray-800'
              }`}
              onClick={() => setIsMenuOpen(false)}
            >
              Contact
            </Link>

            {/* Mobile Icons */}
            <div className="flex items-center justify-center space-x-6 pt-4 border-t border-gray-800 mt-4">
              <Link to="/login" className="p-3 rounded-lg text-gray-300 hover:text-white hover:bg-gray-800 transition-all duration-300">
                <User className="h-6 w-6" />
              </Link>
              <button className="p-3 rounded-lg text-gray-300 hover:text-white hover:bg-gray-800 transition-all duration-300">
                <Heart className="h-6 w-6" />
              </button>
              <button onClick={toggleCart} className="relative p-3 rounded-lg text-gray-300 hover:text-white hover:bg-gray-800 transition-all duration-300">
                <ShoppingCart className="h-6 w-6" />
                {cart.totalItems > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold shadow-lg">
                    {cart.totalItems > 99 ? '99+' : cart.totalItems}
                  </span>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;