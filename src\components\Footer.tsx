import React from 'react';
import { Facebook, Instagram, Twitter, Youtube, Mail, Phone, MapPin } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="border-t" style={{ backgroundColor: '#2a2a2a', borderColor: '#4a4a4a' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold mb-4" style={{ color: '#ef4444' }}>DANFU</h2>
              <p className="leading-relaxed" style={{ color: '#94a3b8' }}>
                Premium sportswear and equipment designed for champions who demand excellence 
                in every training session and competition.
              </p>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center" style={{ color: '#94a3b8' }}>
                <MapPin className="h-5 w-5 mr-3" style={{ color: '#ef4444' }} />
                <span>123 Athletic Avenue, Sports District, CA 90210</span>
              </div>
              <div className="flex items-center" style={{ color: '#94a3b8' }}>
                <Phone className="h-5 w-5 mr-3" style={{ color: '#ef4444' }} />
                <span>******-DANFU-HELP</span>
              </div>
              <div className="flex items-center" style={{ color: '#94a3b8' }}>
                <Mail className="h-5 w-5 mr-3" style={{ color: '#ef4444' }} />
                <span><EMAIL></span>
              </div>
            </div>
          </div>

          {/* Company Links */}
          <div>
            <h3 className="text-lg font-semibold mb-6" style={{ color: '#f8fafc' }}>Company</h3>
            <ul className="space-y-3">
              <li><a href="#" className="transition-colors" style={{ color: '#94a3b8' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#94a3b8'}>About DANFU</a></li>
              <li><a href="#" className="transition-colors" style={{ color: '#94a3b8' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#94a3b8'}>Our Story</a></li>
              <li><a href="#" className="transition-colors" style={{ color: '#94a3b8' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#94a3b8'}>Careers</a></li>
              <li><a href="#" className="transition-colors" style={{ color: '#94a3b8' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#94a3b8'}>Press</a></li>
              <li><a href="#" className="transition-colors" style={{ color: '#94a3b8' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#94a3b8'}>Sustainability</a></li>
            </ul>
          </div>

          {/* Customer Service */}
          <div>
            <h3 className="text-lg font-semibold mb-6" style={{ color: '#f8fafc' }}>Customer Service</h3>
            <ul className="space-y-3">
              <li><a href="#" className="transition-colors" style={{ color: '#94a3b8' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#94a3b8'}>Contact Us</a></li>
              <li><a href="#" className="transition-colors" style={{ color: '#94a3b8' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#94a3b8'}>FAQ</a></li>
              <li><a href="#" className="transition-colors" style={{ color: '#94a3b8' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#94a3b8'}>Shipping & Returns</a></li>
              <li><a href="#" className="transition-colors" style={{ color: '#94a3b8' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#94a3b8'}>Size Guide</a></li>
              <li><a href="#" className="transition-colors" style={{ color: '#94a3b8' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#94a3b8'}>Order Tracking</a></li>
            </ul>
          </div>

          {/* Newsletter & Social */}
          <div>
            <h3 className="text-lg font-semibold mb-6" style={{ color: '#f8fafc' }}>Stay Connected</h3>
            <div className="space-y-4">
              <p style={{ color: '#94a3b8' }}>
                Subscribe to get updates on new products and exclusive offers.
              </p>

              <div className="flex">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 border rounded-l-lg px-4 py-3 focus:outline-none transition-colors"
                  style={{
                    backgroundColor: '#1a1a1a',
                    borderColor: '#4a4a4a',
                    color: '#f8fafc'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#ef4444'}
                  onBlur={(e) => e.target.style.borderColor = '#4a4a4a'}
                />
                <button
                  className="px-6 py-3 rounded-r-lg transition-colors"
                  style={{ backgroundColor: '#ef4444' }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = '#dc2626'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = '#ef4444'}
                >
                  <Mail className="h-5 w-5" style={{ color: '#ffffff' }} />
                </button>
              </div>

              <div className="flex space-x-4 pt-4">
                <a href="#" className="transition-colors" style={{ color: '#94a3b8' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#94a3b8'}>
                  <Facebook className="h-6 w-6" />
                </a>
                <a href="#" className="transition-colors" style={{ color: '#94a3b8' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#94a3b8'}>
                  <Instagram className="h-6 w-6" />
                </a>
                <a href="#" className="transition-colors" style={{ color: '#94a3b8' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#94a3b8'}>
                  <Twitter className="h-6 w-6" />
                </a>
                <a href="#" className="transition-colors" style={{ color: '#94a3b8' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#94a3b8'}>
                  <Youtube className="h-6 w-6" />
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t mt-12 pt-8" style={{ borderColor: '#4a4a4a' }}>
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm" style={{ color: '#94a3b8' }}>
              © 2025 DANFU Sports. All rights reserved.
            </div>

            <div className="flex space-x-6 text-sm">
              <a href="#" className="transition-colors" style={{ color: '#94a3b8' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#94a3b8'}>Privacy Policy</a>
              <a href="#" className="transition-colors" style={{ color: '#94a3b8' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#94a3b8'}>Terms of Service</a>
              <a href="#" className="transition-colors" style={{ color: '#94a3b8' }} onMouseEnter={(e) => e.target.style.color = '#ef4444'} onMouseLeave={(e) => e.target.style.color = '#94a3b8'}>Cookie Policy</a>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm" style={{ color: '#94a3b8' }}>Secure payments:</span>
              <div className="flex space-x-2">
                <div className="rounded px-2 py-1" style={{ backgroundColor: '#f8fafc' }}>
                  <span className="text-xs font-bold" style={{ color: '#1a1a1a' }}>VISA</span>
                </div>
                <div className="rounded px-2 py-1" style={{ backgroundColor: '#f8fafc' }}>
                  <span className="text-xs font-bold" style={{ color: '#1a1a1a' }}>MC</span>
                </div>
                <div className="rounded px-2 py-1" style={{ backgroundColor: '#f8fafc' }}>
                  <span className="text-xs font-bold" style={{ color: '#1a1a1a' }}>AMEX</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;