import React from 'react';
import { Users, Star, Globe, Package, Headphones, Award } from 'lucide-react';

const SuccessSection = () => {
  const achievements = [
    {
      icon: Users,
      number: "500,000+",
      label: "Satisfied Athletes",
      description: "Athletes trust our products"
    },
    {
      icon: Star,
      number: "98%",
      label: "Customer Satisfaction",
      description: "Exceptional quality rating"
    },
    {
      icon: Globe,
      number: "150+",
      label: "Countries Served",
      description: "Global reach and delivery"
    },
    {
      icon: Package,
      number: "2M+",
      label: "Products Delivered",
      description: "Successful deliveries worldwide"
    },
    {
      icon: Headphones,
      number: "24/7",
      label: "Customer Support",
      description: "Always here to help"
    },
    {
      icon: Award,
      number: "15 Years",
      label: "of Excellence",
      description: "Industry experience"
    }
  ];

  return (
    <section className="bg-gray-900 py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-white mb-4">
            Trusted by Champions Worldwide
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Our commitment to excellence has earned the trust of athletes across the globe, 
            delivering premium quality and unmatched performance in every product.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {achievements.map((achievement, index) => {
            const IconComponent = achievement.icon;
            return (
              <div 
                key={index}
                className="bg-black border border-gray-800 rounded-xl p-8 text-center hover:border-red-500 transition-colors group"
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-red-500 rounded-full mb-6 group-hover:scale-110 transition-transform">
                  <IconComponent className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-3xl font-bold text-white mb-2">{achievement.number}</h3>
                <h4 className="text-lg font-semibold text-red-500 mb-2">{achievement.label}</h4>
                <p className="text-gray-400">{achievement.description}</p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default SuccessSection;