// Product related types
export interface Product {
  id: number;
  name: string;
  description?: string;
  price: number;
  originalPrice?: number;
  rating: number;
  reviews: number;
  image: string;
  images?: string[];
  badge?: string;
  category: string;
  subcategory?: string;
  brand?: string;
  sku?: string;
  inventory: {
    quantity: number;
    lowStockThreshold?: number;
  };
  specifications?: {
    size?: string[];
    color?: string[];
    material?: string;
    weight?: number;
    dimensions?: {
      length: number;
      width: number;
      height: number;
    };
  };
  tags?: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Category related types
export interface Category {
  id: number;
  title: string;
  description: string;
  itemCount: string;
  image: string;
  category: 'sportswear' | 'sportsgear';
  parentCategory?: number;
  isActive: boolean;
  sortOrder?: number;
  createdAt: string;
}

// Review related types
export interface Review {
  id: number;
  name: string;
  role: string;
  rating: number;
  comment: string;
  product: string;
  verified: boolean;
  avatar: string;
  userId?: number;
  productId?: number;
  createdAt: string;
}

// User related types
export interface User {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  dateOfBirth?: string;
  accountType: 'customer' | 'seller' | 'admin';
  sellerInfo?: {
    businessName: string;
    businessType: string;
    taxId?: string;
    businessAddress: Address;
    isVerified: boolean;
    verificationDocuments?: string[];
    commissionRate: number;
  };
  isEmailVerified: boolean;
  addresses?: Address[];
  preferences: {
    notifications: boolean;
    marketing: boolean;
    newsletter: boolean;
  };
  loyaltyPoints: number;
  createdAt: string;
  updatedAt: string;
}

export interface Address {
  id: number;
  type: 'billing' | 'shipping';
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  isDefault: boolean;
}

// Order related types
export interface Order {
  id: number;
  userId: number;
  items: OrderItem[];
  totalAmount: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  shippingAddress: Address;
  billingAddress: Address;
  paymentMethod: string;
  tracking?: {
    carrier: string;
    trackingNumber: string;
    estimatedDelivery: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  productId: number;
  quantity: number;
  price: number;
  specifications?: {
    size?: string;
    color?: string;
  };
}

// Brand Partner types
export interface BrandPartner {
  id: number;
  name: string;
  logo: string;
  description: string;
  website?: string;
  isActive: boolean;
  sortOrder?: number;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  message?: string;
}

// Search and Filter types
export interface ProductFilters {
  category?: string;
  subcategory?: string;
  brand?: string;
  minPrice?: number;
  maxPrice?: number;
  rating?: number;
  inStock?: boolean;
  sortBy?: 'price' | 'rating' | 'name' | 'newest';
  sortOrder?: 'asc' | 'desc';
}

export interface SearchParams {
  query?: string;
  filters?: ProductFilters;
  page?: number;
  limit?: number;
}

// Authentication types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignupData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  phoneNumber?: string;
  dateOfBirth?: string;
  acceptTerms: boolean;
  subscribeNewsletter?: boolean;
  accountType?: 'customer' | 'seller';
  // Seller specific fields
  businessName?: string;
  businessType?: string;
  taxId?: string;
}

export interface AuthResponse {
  success: boolean;
  user?: User;
  token?: string;
  refreshToken?: string;
  message?: string;
  error?: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
}

// Cart types
export interface CartItem {
  id: string;
  productId: number;
  product: Product;
  quantity: number;
  selectedSize?: string;
  selectedColor?: string;
  addedAt: string;
}

export interface CartState {
  items: CartItem[];
  totalItems: number;
  totalAmount: number;
  isOpen: boolean;
  loading: boolean;
}

export interface CartContextType {
  cart: CartState;
  addToCart: (product: Product, quantity?: number, options?: { size?: string; color?: string }) => void;
  removeFromCart: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  toggleCart: () => void;
}
