import React from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  ShoppingBag, 
  Users, 
  DollarSign,
  Package,
  Star,
  Eye,
  Heart
} from 'lucide-react';
import DashboardLayout from '../components/DashboardLayout';
import { useAuthState } from '../hooks/useAuth';

const DashboardPage = () => {
  const { authState } = useAuthState();

  const getStatsForRole = () => {
    switch (authState.user?.accountType) {
      case 'admin':
        return [
          { icon: Users, label: 'Total Users', value: '12,543', change: '+12%', color: '#ef4444' },
          { icon: ShoppingBag, label: 'Total Orders', value: '8,234', change: '+8%', color: '#22c55e' },
          { icon: DollarSign, label: 'Revenue', value: '$124,567', change: '+15%', color: '#2a2a2a' },
          { icon: Package, label: 'Products', value: '1,234', change: '+5%', color: '#f59e0b' }
        ];
      case 'seller':
        return [
          { icon: Package, label: 'My Products', value: '45', change: '+3', color: '#ef4444' },
          { icon: ShoppingBag, label: 'Orders', value: '123', change: '+12%', color: '#22c55e' },
          { icon: DollarSign, label: 'Earnings', value: '$5,678', change: '+18%', color: '#2a2a2a' },
          { icon: Star, label: 'Rating', value: '4.8', change: '+0.2', color: '#f59e0b' }
        ];
      case 'customer':
      default:
        return [
          { icon: ShoppingBag, label: 'Orders', value: '12', change: '+2', color: '#ef4444' },
          { icon: Heart, label: 'Wishlist', value: '8', change: '+1', color: '#22c55e' },
          { icon: DollarSign, label: 'Spent', value: '$1,234', change: '+$200', color: '#2a2a2a' },
          { icon: Star, label: 'Reviews', value: '15', change: '+3', color: '#f59e0b' }
        ];
    }
  };

  const stats = getStatsForRole();

  const getRecentActivity = () => {
    switch (authState.user?.accountType) {
      case 'admin':
        return [
          { action: 'New user registered', user: 'John Doe', time: '2 minutes ago' },
          { action: 'Order completed', user: 'Sarah Smith', time: '5 minutes ago' },
          { action: 'Product added', user: 'Mike Johnson', time: '10 minutes ago' },
          { action: 'Seller verified', user: 'Tech Store', time: '15 minutes ago' }
        ];
      case 'seller':
        return [
          { action: 'New order received', user: 'Order #1234', time: '1 hour ago' },
          { action: 'Product viewed', user: 'Running Shoes', time: '2 hours ago' },
          { action: 'Review received', user: '5 stars', time: '3 hours ago' },
          { action: 'Payment received', user: '$89.99', time: '4 hours ago' }
        ];
      case 'customer':
      default:
        return [
          { action: 'Order shipped', user: 'Order #5678', time: '1 day ago' },
          { action: 'Item added to wishlist', user: 'Nike Shoes', time: '2 days ago' },
          { action: 'Review submitted', user: 'Adidas T-shirt', time: '3 days ago' },
          { action: 'Account updated', user: 'Profile info', time: '1 week ago' }
        ];
    }
  };

  const recentActivity = getRecentActivity();

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-3xl font-bold mb-2" style={{ color: '#f8fafc' }}>
            Welcome back, {authState.user?.firstName}!
          </h1>
          <p style={{ color: '#94a3b8' }}>
            Here's what's happening with your {authState.user?.accountType} account today.
          </p>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={index}
                className="p-6 rounded-xl border"
                style={{ backgroundColor: '#1a1a1a', borderColor: '#4a4a4a' }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -5, scale: 1.02 }}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 rounded-lg" style={{ backgroundColor: `${stat.color}20` }}>
                    <Icon className="h-6 w-6" style={{ color: stat.color }} />
                  </div>
                  <span className="text-sm font-medium" style={{ color: '#22c55e' }}>
                    {stat.change}
                  </span>
                </div>
                <h3 className="text-2xl font-bold mb-1" style={{ color: '#f8fafc' }}>
                  {stat.value}
                </h3>
                <p className="text-sm" style={{ color: '#94a3b8' }}>
                  {stat.label}
                </p>
              </motion.div>
            );
          })}
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Activity */}
          <motion.div
            className="p-6 rounded-xl border"
            style={{ backgroundColor: '#1a1a1a', borderColor: '#4a4a4a' }}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h2 className="text-xl font-bold mb-4" style={{ color: '#f8fafc' }}>
              Recent Activity
            </h2>
            <div className="space-y-4">
              {recentActivity.map((activity, index) => (
                <motion.div
                  key={index}
                  className="flex items-center justify-between p-3 rounded-lg"
                  style={{ backgroundColor: '#2a2a2a' }}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 + index * 0.1 }}
                >
                  <div>
                    <p className="font-medium" style={{ color: '#f8fafc' }}>
                      {activity.action}
                    </p>
                    <p className="text-sm" style={{ color: '#94a3b8' }}>
                      {activity.user}
                    </p>
                  </div>
                  <span className="text-xs" style={{ color: '#94a3b8' }}>
                    {activity.time}
                  </span>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            className="p-6 rounded-xl border"
            style={{ backgroundColor: '#1a1a1a', borderColor: '#4a4a4a' }}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <h2 className="text-xl font-bold mb-4" style={{ color: '#f8fafc' }}>
              Quick Actions
            </h2>
            <div className="grid grid-cols-2 gap-4">
              {authState.user?.accountType === 'admin' && (
                <>
                  <motion.button
                    className="p-4 rounded-lg border text-center transition-colors"
                    style={{ borderColor: '#4a4a4a', color: '#94a3b8' }}
                    whileHover={{ borderColor: '#ef4444', color: '#f8fafc', scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Users className="h-6 w-6 mx-auto mb-2" />
                    <span className="text-sm font-medium">Manage Users</span>
                  </motion.button>
                  <motion.button
                    className="p-4 rounded-lg border text-center transition-colors"
                    style={{ borderColor: '#4a4a4a', color: '#94a3b8' }}
                    whileHover={{ borderColor: '#ef4444', color: '#f8fafc', scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Package className="h-6 w-6 mx-auto mb-2" />
                    <span className="text-sm font-medium">Add Product</span>
                  </motion.button>
                </>
              )}
              
              {authState.user?.accountType === 'seller' && (
                <>
                  <motion.button
                    className="p-4 rounded-lg border text-center transition-colors"
                    style={{ borderColor: '#4a4a4a', color: '#94a3b8' }}
                    whileHover={{ borderColor: '#ef4444', color: '#f8fafc', scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Package className="h-6 w-6 mx-auto mb-2" />
                    <span className="text-sm font-medium">Add Product</span>
                  </motion.button>
                  <motion.button
                    className="p-4 rounded-lg border text-center transition-colors"
                    style={{ borderColor: '#4a4a4a', color: '#94a3b8' }}
                    whileHover={{ borderColor: '#ef4444', color: '#f8fafc', scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <TrendingUp className="h-6 w-6 mx-auto mb-2" />
                    <span className="text-sm font-medium">View Analytics</span>
                  </motion.button>
                </>
              )}
              
              {authState.user?.accountType === 'customer' && (
                <>
                  <motion.button
                    className="p-4 rounded-lg border text-center transition-colors"
                    style={{ borderColor: '#4a4a4a', color: '#94a3b8' }}
                    whileHover={{ borderColor: '#ef4444', color: '#f8fafc', scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <ShoppingBag className="h-6 w-6 mx-auto mb-2" />
                    <span className="text-sm font-medium">View Orders</span>
                  </motion.button>
                  <motion.button
                    className="p-4 rounded-lg border text-center transition-colors"
                    style={{ borderColor: '#4a4a4a', color: '#94a3b8' }}
                    whileHover={{ borderColor: '#ef4444', color: '#f8fafc', scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Heart className="h-6 w-6 mx-auto mb-2" />
                    <span className="text-sm font-medium">Wishlist</span>
                  </motion.button>
                </>
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default DashboardPage;
