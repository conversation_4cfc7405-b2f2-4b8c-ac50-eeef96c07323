@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables for Black & Gray Color Scheme */
:root {
  /* Primary Colors */
  --color-primary: #000000;
  --color-primary-light: #1a1a1a;
  --color-secondary: #2a2a2a;
  --color-accent: #ef4444;
  --color-accent-hover: #dc2626;
  --color-accent-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);

  /* Background Colors */
  --color-bg-primary: #000000;
  --color-bg-secondary: #1a1a1a;
  --color-bg-card: #2a2a2a;
  --color-bg-elevated: #3a3a3a;

  /* Text Colors */
  --color-text-primary: #ffffff;
  --color-text-secondary: #d1d5db;
  --color-text-muted: #9ca3af;

  /* Border Colors */
  --color-border: #404040;
  --color-border-light: #525252;

  /* Status Colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
}

/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  background: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--color-accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent-hover);
}

/* Utility Classes */
.gradient-bg {
  background: linear-gradient(135deg, var(--color-bg-primary) 0%, var(--color-bg-secondary) 100%);
}

.gradient-accent {
  background: var(--color-accent-gradient);
}

.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(42, 42, 42, 0.8);
  border: 1px solid rgba(64, 64, 64, 0.3);
}

.card-shadow {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

.card-shadow-hover {
  box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.6), 0 8px 16px -4px rgba(0, 0, 0, 0.4);
}

/* Button Styles */
.btn-primary {
  background: var(--color-accent-gradient);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.btn-secondary {
  background: var(--color-bg-card);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: var(--color-bg-elevated);
  border-color: var(--color-accent);
  transform: translateY(-1px);
}

/* Input Styles */
.input-modern {
  background: var(--color-bg-card);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 12px 16px;
  color: var(--color-text-primary);
  transition: all 0.3s ease;
}

.input-modern:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-modern::placeholder {
  color: var(--color-text-muted);
}
