import {
  Product,
  Category,
  Review,
  BrandPartner,
  User,
  ApiResponse,
  PaginatedResponse,
  SearchParams,
  ProductFilters,
  LoginCredentials,
  SignupData,
  AuthResponse
} from '../types';

// Mock data
const mockProducts: Product[] = [
  {
    id: 1,
    name: "DANFU Pro Running Shoes",
    description: "Professional running shoes designed for maximum performance and comfort during long-distance runs.",
    price: 149.99,
    originalPrice: 199.99,
    rating: 4.8,
    reviews: 324,
    image: "https://images.pexels.com/photos/2529148/pexels-photo-2529148.jpeg?auto=compress&cs=tinysrgb&w=400",
    badge: "Best Seller",
    category: "footwear",
    subcategory: "running",
    brand: "DANFU",
    sku: "DF-RS-001",
    inventory: {
      quantity: 150,
      lowStockThreshold: 20
    },
    specifications: {
      size: ["7", "8", "9", "10", "11", "12"],
      color: ["Black", "White", "Red"],
      material: "Synthetic mesh with rubber sole",
      weight: 280
    },
    tags: ["running", "professional", "comfortable"],
    isActive: true,
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-20T15:30:00Z"
  },
  {
    id: 2,
    name: "Elite Performance T-Shirt",
    description: "Moisture-wicking performance t-shirt perfect for intense training sessions.",
    price: 39.99,
    rating: 4.9,
    reviews: 156,
    image: "https://images.pexels.com/photos/8532616/pexels-photo-8532616.jpeg?auto=compress&cs=tinysrgb&w=400",
    badge: "New",
    category: "apparel",
    subcategory: "shirts",
    brand: "DANFU",
    sku: "DF-TS-002",
    inventory: {
      quantity: 200,
      lowStockThreshold: 30
    },
    specifications: {
      size: ["S", "M", "L", "XL", "XXL"],
      color: ["Black", "White", "Navy", "Red"],
      material: "100% Polyester with moisture-wicking technology"
    },
    tags: ["training", "moisture-wicking", "comfortable"],
    isActive: true,
    createdAt: "2024-01-10T08:00:00Z",
    updatedAt: "2024-01-18T12:00:00Z"
  },
  {
    id: 3,
    name: "Professional Soccer Cleats",
    description: "High-performance soccer cleats designed for professional players and serious enthusiasts.",
    price: 199.99,
    rating: 4.7,
    reviews: 89,
    image: "https://images.pexels.com/photos/2526878/pexels-photo-2526878.jpeg?auto=compress&cs=tinysrgb&w=400",
    badge: "Popular",
    category: "footwear",
    subcategory: "soccer",
    brand: "DANFU",
    sku: "DF-SC-003",
    inventory: {
      quantity: 75,
      lowStockThreshold: 15
    },
    specifications: {
      size: ["6", "7", "8", "9", "10", "11", "12"],
      color: ["Black", "White", "Blue"],
      material: "Synthetic leather with rubber studs"
    },
    tags: ["soccer", "professional", "cleats"],
    isActive: true,
    createdAt: "2024-01-05T14:00:00Z",
    updatedAt: "2024-01-22T09:15:00Z"
  },
  {
    id: 4,
    name: "DANFU Basketball Shorts",
    description: "Lightweight basketball shorts with moisture-wicking fabric and comfortable fit for intense games.",
    price: 49.99,
    originalPrice: 69.99,
    rating: 4.6,
    reviews: 203,
    image: "https://images.pexels.com/photos/7045988/pexels-photo-7045988.jpeg?auto=compress&cs=tinysrgb&w=400",
    badge: "Sale",
    category: "apparel",
    subcategory: "shorts",
    brand: "DANFU",
    sku: "DF-BS-004",
    inventory: {
      quantity: 120,
      lowStockThreshold: 25
    },
    specifications: {
      size: ["S", "M", "L", "XL", "XXL"],
      color: ["Black", "Navy", "Red", "White"],
      material: "100% Polyester with DRI-FIT technology"
    },
    tags: ["basketball", "shorts", "moisture-wicking", "lightweight"],
    isActive: true,
    createdAt: "2024-01-12T10:00:00Z",
    updatedAt: "2024-01-25T14:30:00Z"
  },
  {
    id: 5,
    name: "Pro Tennis Racket",
    description: "Professional-grade tennis racket with carbon fiber construction for power and precision.",
    price: 299.99,
    rating: 4.9,
    reviews: 67,
    image: "https://images.pexels.com/photos/209977/pexels-photo-209977.jpeg?auto=compress&cs=tinysrgb&w=400",
    badge: "Premium",
    category: "equipment",
    subcategory: "tennis",
    brand: "DANFU",
    sku: "DF-TR-005",
    inventory: {
      quantity: 45,
      lowStockThreshold: 10
    },
    specifications: {
      color: ["Black/Red", "White/Blue", "Black/Yellow"],
      material: "Carbon Fiber Frame with Synthetic Strings",
      weight: 300,
      dimensions: {
        length: 27,
        width: 11,
        height: 1
      }
    },
    tags: ["tennis", "racket", "professional", "carbon-fiber"],
    isActive: true,
    createdAt: "2024-01-08T16:00:00Z",
    updatedAt: "2024-01-20T11:45:00Z"
  },
  {
    id: 6,
    name: "Compression Training Leggings",
    description: "High-performance compression leggings designed for intense training and recovery.",
    price: 79.99,
    rating: 4.8,
    reviews: 189,
    image: "https://images.pexels.com/photos/6551415/pexels-photo-6551415.jpeg?auto=compress&cs=tinysrgb&w=400",
    badge: "Best Seller",
    category: "apparel",
    subcategory: "leggings",
    brand: "DANFU",
    sku: "DF-CL-006",
    inventory: {
      quantity: 180,
      lowStockThreshold: 30
    },
    specifications: {
      size: ["XS", "S", "M", "L", "XL"],
      color: ["Black", "Navy", "Charcoal", "Purple"],
      material: "88% Polyester, 12% Elastane with compression technology"
    },
    tags: ["compression", "leggings", "training", "recovery"],
    isActive: true,
    createdAt: "2024-01-15T09:30:00Z",
    updatedAt: "2024-01-28T13:20:00Z"
  },
  {
    id: 7,
    name: "Wireless Sports Headphones",
    description: "Sweat-resistant wireless headphones with superior sound quality for your workouts.",
    price: 129.99,
    originalPrice: 159.99,
    rating: 4.7,
    reviews: 145,
    image: "https://images.pexels.com/photos/3394650/pexels-photo-3394650.jpeg?auto=compress&cs=tinysrgb&w=400",
    badge: "Tech",
    category: "equipment",
    subcategory: "audio",
    brand: "DANFU",
    sku: "DF-WH-007",
    inventory: {
      quantity: 85,
      lowStockThreshold: 15
    },
    specifications: {
      color: ["Black", "White", "Blue", "Red"],
      material: "Sweat-resistant plastic with memory foam ear cups",
      weight: 250
    },
    tags: ["wireless", "headphones", "sweat-resistant", "bluetooth"],
    isActive: true,
    createdAt: "2024-01-18T12:00:00Z",
    updatedAt: "2024-01-30T16:15:00Z"
  },
  {
    id: 8,
    name: "Yoga Mat Premium",
    description: "Extra-thick premium yoga mat with superior grip and cushioning for all yoga practices.",
    price: 89.99,
    rating: 4.9,
    reviews: 234,
    image: "https://images.pexels.com/photos/3822622/pexels-photo-3822622.jpeg?auto=compress&cs=tinysrgb&w=400",
    badge: "Eco-Friendly",
    category: "equipment",
    subcategory: "yoga",
    brand: "DANFU",
    sku: "DF-YM-008",
    inventory: {
      quantity: 95,
      lowStockThreshold: 20
    },
    specifications: {
      color: ["Purple", "Blue", "Green", "Pink", "Black"],
      material: "Natural rubber with microfiber top layer",
      dimensions: {
        length: 72,
        width: 24,
        height: 0.25
      }
    },
    tags: ["yoga", "mat", "eco-friendly", "non-slip"],
    isActive: true,
    createdAt: "2024-01-10T14:45:00Z",
    updatedAt: "2024-01-26T10:30:00Z"
  },
  {
    id: 9,
    name: "Adjustable Dumbbells Set",
    description: "Space-saving adjustable dumbbells with quick-change weight system for home workouts.",
    price: 399.99,
    originalPrice: 499.99,
    rating: 4.8,
    reviews: 98,
    image: "https://images.pexels.com/photos/4164761/pexels-photo-4164761.jpeg?auto=compress&cs=tinysrgb&w=400",
    badge: "Heavy Duty",
    category: "equipment",
    subcategory: "weights",
    brand: "DANFU",
    sku: "DF-AD-009",
    inventory: {
      quantity: 35,
      lowStockThreshold: 8
    },
    specifications: {
      color: ["Black/Silver"],
      material: "Cast iron with rubber coating",
      weight: 5000,
      dimensions: {
        length: 16,
        width: 8,
        height: 8
      }
    },
    tags: ["dumbbells", "adjustable", "home-gym", "strength-training"],
    isActive: true,
    createdAt: "2024-01-06T11:20:00Z",
    updatedAt: "2024-01-24T15:40:00Z"
  },
  {
    id: 10,
    name: "Swimming Goggles Pro",
    description: "Professional swimming goggles with anti-fog coating and UV protection for competitive swimming.",
    price: 34.99,
    rating: 4.6,
    reviews: 156,
    image: "https://images.pexels.com/photos/863988/pexels-photo-863988.jpeg?auto=compress&cs=tinysrgb&w=400",
    badge: "Waterproof",
    category: "equipment",
    subcategory: "swimming",
    brand: "DANFU",
    sku: "DF-SG-010",
    inventory: {
      quantity: 140,
      lowStockThreshold: 25
    },
    specifications: {
      color: ["Clear/Blue", "Black/Silver", "Red/White", "Purple/Pink"],
      material: "Silicone gasket with polycarbonate lens"
    },
    tags: ["swimming", "goggles", "anti-fog", "uv-protection"],
    isActive: true,
    createdAt: "2024-01-14T08:15:00Z",
    updatedAt: "2024-01-27T12:50:00Z"
  },
  {
    id: 11,
    name: "Cycling Jersey Pro",
    description: "Aerodynamic cycling jersey with moisture-wicking fabric and reflective elements for safety.",
    price: 69.99,
    originalPrice: 89.99,
    rating: 4.7,
    reviews: 112,
    image: "https://images.pexels.com/photos/6386956/pexels-photo-6386956.jpeg?auto=compress&cs=tinysrgb&w=400",
    badge: "Reflective",
    category: "apparel",
    subcategory: "cycling",
    brand: "DANFU",
    sku: "DF-CJ-011",
    inventory: {
      quantity: 75,
      lowStockThreshold: 15
    },
    specifications: {
      size: ["S", "M", "L", "XL", "XXL"],
      color: ["Blue/White", "Red/Black", "Green/Yellow", "Black/Silver"],
      material: "100% Polyester with reflective strips"
    },
    tags: ["cycling", "jersey", "aerodynamic", "reflective", "safety"],
    isActive: true,
    createdAt: "2024-01-11T13:30:00Z",
    updatedAt: "2024-01-29T09:25:00Z"
  },
  {
    id: 12,
    name: "Resistance Bands Set",
    description: "Complete resistance bands set with multiple resistance levels for full-body workouts.",
    price: 59.99,
    rating: 4.8,
    reviews: 287,
    image: "https://images.pexels.com/photos/4164766/pexels-photo-4164766.jpeg?auto=compress&cs=tinysrgb&w=400",
    badge: "Complete Set",
    category: "equipment",
    subcategory: "fitness",
    brand: "DANFU",
    sku: "DF-RB-012",
    inventory: {
      quantity: 110,
      lowStockThreshold: 20
    },
    specifications: {
      color: ["Multi-Color Set"],
      material: "Natural latex with foam handles"
    },
    tags: ["resistance-bands", "full-body", "portable", "strength-training"],
    isActive: true,
    createdAt: "2024-01-09T15:45:00Z",
    updatedAt: "2024-01-31T11:10:00Z"
  },
  {
    id: 13,
    name: "Running Jacket Windproof",
    description: "Lightweight windproof running jacket with breathable fabric and reflective details.",
    price: 119.99,
    originalPrice: 149.99,
    rating: 4.9,
    reviews: 178,
    image: "https://images.pexels.com/photos/7045991/pexels-photo-7045991.jpeg?auto=compress&cs=tinysrgb&w=400",
    badge: "Weather Shield",
    category: "apparel",
    subcategory: "jackets",
    brand: "DANFU",
    sku: "DF-RJ-013",
    inventory: {
      quantity: 65,
      lowStockThreshold: 12
    },
    specifications: {
      size: ["XS", "S", "M", "L", "XL", "XXL"],
      color: ["Black", "Navy", "Charcoal", "Neon Green"],
      material: "Windproof polyester with mesh lining"
    },
    tags: ["running", "jacket", "windproof", "breathable", "reflective"],
    isActive: true,
    createdAt: "2024-01-07T10:00:00Z",
    updatedAt: "2024-01-25T14:35:00Z"
  }
];

const mockCategories: Category[] = [
  {
    id: 1,
    title: "Athletic Footwear",
    description: "Performance shoes for every sport",
    itemCount: "150+ items",
    image: "https://images.pexels.com/photos/2529148/pexels-photo-2529148.jpeg?auto=compress&cs=tinysrgb&w=600",
    category: "sportswear",
    isActive: true,
    sortOrder: 1,
    createdAt: "2024-01-01T00:00:00Z"
  },
  {
    id: 2,
    title: "Training Apparel",
    description: "Premium workout clothing",
    itemCount: "200+ items",
    image: "https://images.pexels.com/photos/8532616/pexels-photo-8532616.jpeg?auto=compress&cs=tinysrgb&w=600",
    category: "sportswear",
    isActive: true,
    sortOrder: 2,
    createdAt: "2024-01-01T00:00:00Z"
  },
  {
    id: 3,
    title: "Fitness Equipment",
    description: "Professional training gear",
    itemCount: "80+ items",
    image: "https://images.pexels.com/photos/4162449/pexels-photo-4162449.jpeg?auto=compress&cs=tinysrgb&w=600",
    category: "sportsgear",
    isActive: true,
    sortOrder: 3,
    createdAt: "2024-01-01T00:00:00Z"
  }
];

const mockReviews: Review[] = [
  {
    id: 1,
    name: "Sarah Johnson",
    role: "Marathon Runner",
    rating: 5,
    comment: "The quality of DANFU products exceeded my expectations. The running shoes provided excellent support during my marathon training, and the customer service was outstanding.",
    product: "Pro Running Shoes",
    verified: true,
    avatar: "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100",
    userId: 1,
    productId: 1,
    createdAt: "2024-01-20T10:30:00Z"
  },
  {
    id: 2,
    name: "Michael Chen",
    role: "Fitness Trainer",
    rating: 5,
    comment: "Outstanding customer service and fast delivery. The compression wear fits perfectly and enhances my workout performance. I recommend DANFU to all my clients.",
    product: "Elite Compression Set",
    verified: true,
    avatar: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=100",
    userId: 2,
    productId: 2,
    createdAt: "2024-01-18T14:15:00Z"
  }
];

const mockBrandPartners: BrandPartner[] = [
  {
    id: 1,
    name: "Nike",
    logo: "https://logos-world.net/wp-content/uploads/2020/04/Nike-Logo.png",
    description: "Global sportswear leader",
    website: "https://nike.com",
    isActive: true,
    sortOrder: 1
  },
  {
    id: 2,
    name: "Adidas",
    logo: "https://logos-world.net/wp-content/uploads/2020/04/Adidas-Logo.png",
    description: "Performance innovation",
    website: "https://adidas.com",
    isActive: true,
    sortOrder: 2
  }
];

// Mock users for authentication
const mockUsers: User[] = [
  {
    id: 1,
    email: "<EMAIL>",
    firstName: "Admin",
    lastName: "User",
    phoneNumber: "+*********0",
    accountType: "admin",
    isEmailVerified: true,
    preferences: {
      notifications: true,
      marketing: true,
      newsletter: true
    },
    loyaltyPoints: 1000,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z"
  },
  {
    id: 2,
    email: "<EMAIL>",
    firstName: "John",
    lastName: "Doe",
    phoneNumber: "+**********",
    accountType: "customer",
    isEmailVerified: true,
    preferences: {
      notifications: true,
      marketing: false,
      newsletter: true
    },
    loyaltyPoints: 250,
    createdAt: "2024-01-15T00:00:00Z",
    updatedAt: "2024-01-20T00:00:00Z"
  },
  {
    id: 3,
    email: "<EMAIL>",
    firstName: "Sarah",
    lastName: "Johnson",
    phoneNumber: "+**********",
    accountType: "customer",
    isEmailVerified: true,
    preferences: {
      notifications: true,
      marketing: true,
      newsletter: true
    },
    loyaltyPoints: 750,
    createdAt: "2024-02-01T00:00:00Z",
    updatedAt: "2024-02-15T00:00:00Z"
  },
  {
    id: 4,
    email: "<EMAIL>",
    firstName: "Mike",
    lastName: "Store",
    phoneNumber: "+**********",
    accountType: "seller",
    isEmailVerified: true,
    preferences: {
      notifications: true,
      marketing: true,
      newsletter: true
    },
    loyaltyPoints: 0,
    sellerInfo: {
      businessName: "Mike's Sports Store",
      businessType: "retail",
      taxId: "*********",
      businessAddress: {
        street: "123 Business St",
        city: "Commerce City",
        state: "CA",
        zipCode: "90210",
        country: "USA"
      },
      isVerified: true,
      verificationDocuments: ["business_license.pdf", "tax_id.pdf"],
      commissionRate: 0.15
    },
    createdAt: "2024-01-10T00:00:00Z",
    updatedAt: "2024-02-01T00:00:00Z"
  }
];

// Mock passwords (in real app, these would be hashed)
const mockPasswords: Record<string, string> = {
  "<EMAIL>": "admin123",
  "<EMAIL>": "customer123",
  "<EMAIL>": "athlete123",
  "<EMAIL>": "seller123"
};

// Utility function to simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Mock API functions
export const mockApi = {
  // Products
  async getProducts(params?: SearchParams): Promise<PaginatedResponse<Product>> {
    await delay(500); // Simulate network delay
    
    let filteredProducts = [...mockProducts];
    
    // Apply search query
    if (params?.query) {
      const query = params.query.toLowerCase();
      filteredProducts = filteredProducts.filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.description?.toLowerCase().includes(query) ||
        product.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }
    
    // Apply filters
    if (params?.filters) {
      const filters = params.filters;
      
      if (filters.category) {
        filteredProducts = filteredProducts.filter(p => p.category === filters.category);
      }
      
      if (filters.minPrice !== undefined) {
        filteredProducts = filteredProducts.filter(p => p.price >= filters.minPrice!);
      }
      
      if (filters.maxPrice !== undefined) {
        filteredProducts = filteredProducts.filter(p => p.price <= filters.maxPrice!);
      }
      
      if (filters.rating !== undefined) {
        filteredProducts = filteredProducts.filter(p => p.rating >= filters.rating!);
      }
      
      if (filters.inStock) {
        filteredProducts = filteredProducts.filter(p => p.inventory.quantity > 0);
      }
    }
    
    // Apply sorting
    if (params?.filters?.sortBy) {
      const { sortBy, sortOrder = 'asc' } = params.filters;
      filteredProducts.sort((a, b) => {
        let aVal: any, bVal: any;
        
        switch (sortBy) {
          case 'price':
            aVal = a.price;
            bVal = b.price;
            break;
          case 'rating':
            aVal = a.rating;
            bVal = b.rating;
            break;
          case 'name':
            aVal = a.name;
            bVal = b.name;
            break;
          case 'newest':
            aVal = new Date(a.createdAt);
            bVal = new Date(b.createdAt);
            break;
          default:
            return 0;
        }
        
        if (sortOrder === 'desc') {
          return aVal < bVal ? 1 : aVal > bVal ? -1 : 0;
        } else {
          return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
        }
      });
    }
    
    // Apply pagination
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex);
    
    return {
      success: true,
      data: paginatedProducts,
      pagination: {
        page,
        limit,
        total: filteredProducts.length,
        totalPages: Math.ceil(filteredProducts.length / limit)
      }
    };
  },

  async getProductById(id: number): Promise<ApiResponse<Product>> {
    await delay(300);
    const product = mockProducts.find(p => p.id === id);
    
    if (!product) {
      return {
        success: false,
        data: {} as Product,
        error: 'Product not found'
      };
    }
    
    return {
      success: true,
      data: product
    };
  },

  // Categories
  async getCategories(): Promise<ApiResponse<Category[]>> {
    await delay(200);
    return {
      success: true,
      data: mockCategories
    };
  },

  // Reviews
  async getReviews(): Promise<ApiResponse<Review[]>> {
    await delay(300);
    return {
      success: true,
      data: mockReviews
    };
  },

  // Brand Partners
  async getBrandPartners(): Promise<ApiResponse<BrandPartner[]>> {
    await delay(200);
    return {
      success: true,
      data: mockBrandPartners
    };
  },

  // Authentication
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    await delay(800); // Simulate network delay

    const { email, password } = credentials;

    // Find user by email
    const user = mockUsers.find(u => u.email.toLowerCase() === email.toLowerCase());

    if (!user) {
      return {
        success: false,
        error: "User not found. Please check your email address."
      };
    }

    // Check password
    if (mockPasswords[user.email] !== password) {
      return {
        success: false,
        error: "Invalid password. Please try again."
      };
    }

    // Generate mock JWT token
    const token = `mock_jwt_token_${user.id}_${Date.now()}`;
    const refreshToken = `mock_refresh_token_${user.id}_${Date.now()}`;

    return {
      success: true,
      user,
      token,
      refreshToken,
      message: "Login successful! Welcome back."
    };
  },

  async signup(signupData: SignupData): Promise<AuthResponse> {
    await delay(1000); // Simulate network delay

    const { email, password, confirmPassword, firstName, lastName, phoneNumber, acceptTerms } = signupData;

    // Validation
    if (!acceptTerms) {
      return {
        success: false,
        error: "You must accept the terms and conditions to create an account."
      };
    }

    if (password !== confirmPassword) {
      return {
        success: false,
        error: "Passwords do not match. Please try again."
      };
    }

    if (password.length < 6) {
      return {
        success: false,
        error: "Password must be at least 6 characters long."
      };
    }

    // Check if user already exists
    const existingUser = mockUsers.find(u => u.email.toLowerCase() === email.toLowerCase());
    if (existingUser) {
      return {
        success: false,
        error: "An account with this email already exists. Please try logging in instead."
      };
    }

    // Create new user
    const newUser: User = {
      id: mockUsers.length + 1,
      email: email.toLowerCase(),
      firstName,
      lastName,
      phoneNumber,
      accountType: signupData.accountType || "customer",
      isEmailVerified: false,
      preferences: {
        notifications: true,
        marketing: signupData.subscribeNewsletter || false,
        newsletter: signupData.subscribeNewsletter || false
      },
      loyaltyPoints: 0,
      ...(signupData.accountType === 'seller' && signupData.businessName && {
        sellerInfo: {
          businessName: signupData.businessName,
          businessType: signupData.businessType || 'retail',
          taxId: signupData.taxId,
          businessAddress: {
            street: '',
            city: '',
            state: '',
            zipCode: '',
            country: 'USA'
          },
          isVerified: false,
          verificationDocuments: [],
          commissionRate: 0.15
        }
      }),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add to mock database
    mockUsers.push(newUser);
    mockPasswords[newUser.email] = password;

    // Generate tokens
    const token = `mock_jwt_token_${newUser.id}_${Date.now()}`;
    const refreshToken = `mock_refresh_token_${newUser.id}_${Date.now()}`;

    return {
      success: true,
      user: newUser,
      token,
      refreshToken,
      message: "Account created successfully! Welcome to DANFU."
    };
  },

  async logout(): Promise<ApiResponse<null>> {
    await delay(300);
    return {
      success: true,
      data: null,
      message: "Logged out successfully."
    };
  },

  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    await delay(500);

    // In a real app, you'd validate the refresh token
    if (!refreshToken.startsWith('mock_refresh_token_')) {
      return {
        success: false,
        error: "Invalid refresh token."
      };
    }

    // Extract user ID from token (mock implementation)
    const userId = parseInt(refreshToken.split('_')[3]);
    const user = mockUsers.find(u => u.id === userId);

    if (!user) {
      return {
        success: false,
        error: "User not found."
      };
    }

    const newToken = `mock_jwt_token_${user.id}_${Date.now()}`;
    const newRefreshToken = `mock_refresh_token_${user.id}_${Date.now()}`;

    return {
      success: true,
      user,
      token: newToken,
      refreshToken: newRefreshToken,
      message: "Token refreshed successfully."
    };
  }
};
