# DANFU API Testing Guide

## 🚀 Authentication API Testing

### Demo Credentials
Use these credentials to test the authentication system:

#### Admin Account
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Role**: Admin
- **Features**: Full access to all features

#### Customer Account
- **Email**: `<EMAIL>`
- **Password**: `customer123`
- **Role**: Customer
- **Features**: Standard customer features

#### Seller Account
- **Email**: `<EMAIL>`
- **Password**: `seller123`
- **Role**: Seller
- **Features**: Product management, order tracking, sales analytics

#### Athlete Account
- **Email**: `<EMAIL>`
- **Password**: `athlete123`
- **Role**: Customer (Athlete)
- **Features**: Enhanced customer features with athlete perks

---

## 📋 Postman Collection

### Base URL
```
http://localhost:5174
```

### 1. Login API Test

**Method**: `POST`
**Endpoint**: `/api/auth/login` (Mock implementation)
**Headers**:
```json
{
  "Content-Type": "application/json"
}
```

**Body** (JSON):
```json
{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

**Expected Response** (Success):
```json
{
  "success": true,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "Admin",
    "lastName": "User",
    "accountType": "admin",
    "isEmailVerified": true,
    "loyaltyPoints": 1000
  },
  "token": "mock_jwt_token_1_**********",
  "refreshToken": "mock_refresh_token_1_**********",
  "message": "Login successful! Welcome back."
}
```

**Expected Response** (Error):
```json
{
  "success": false,
  "error": "Invalid password. Please try again."
}
```

### 2. Signup API Test

**Method**: `POST`
**Endpoint**: `/api/auth/signup` (Mock implementation)
**Headers**:
```json
{
  "Content-Type": "application/json"
}
```

**Body** (JSON):
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "confirmPassword": "password123",
  "phoneNumber": "+**********",
  "acceptTerms": true,
  "subscribeNewsletter": true
}
```

**Expected Response** (Success):
```json
{
  "success": true,
  "user": {
    "id": 4,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "accountType": "customer",
    "isEmailVerified": false,
    "loyaltyPoints": 0
  },
  "token": "mock_jwt_token_4_**********",
  "refreshToken": "mock_refresh_token_4_**********",
  "message": "Account created successfully! Welcome to DANFU."
}
```

### 3. Logout API Test

**Method**: `POST`
**Endpoint**: `/api/auth/logout` (Mock implementation)
**Headers**:
```json
{
  "Content-Type": "application/json",
  "Authorization": "Bearer mock_jwt_token_1_**********"
}
```

**Expected Response**:
```json
{
  "success": true,
  "data": null,
  "message": "Logged out successfully."
}
```

### 4. Refresh Token API Test

**Method**: `POST`
**Endpoint**: `/api/auth/refresh` (Mock implementation)
**Headers**:
```json
{
  "Content-Type": "application/json"
}
```

**Body** (JSON):
```json
{
  "refreshToken": "mock_refresh_token_1_**********"
}
```

**Expected Response**:
```json
{
  "success": true,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "Admin",
    "lastName": "User"
  },
  "token": "mock_jwt_token_1_1234567891",
  "refreshToken": "mock_refresh_token_1_1234567891",
  "message": "Token refreshed successfully."
}
```

---

## 🧪 Testing Scenarios

### Valid Login Tests
1. **Admin Login**: Use `<EMAIL>` / `admin123`
2. **Customer Login**: Use `<EMAIL>` / `customer123`
3. **Athlete Login**: Use `<EMAIL>` / `athlete123`

### Invalid Login Tests
1. **Wrong Email**: Use `<EMAIL>` / `admin123`
2. **Wrong Password**: Use `<EMAIL>` / `wrongpassword`
3. **Empty Fields**: Send empty email or password
4. **Invalid Email Format**: Use `invalid-email` / `admin123`

### Valid Signup Tests
1. **New User**: Use unique email with valid data
2. **Newsletter Subscription**: Test with `subscribeNewsletter: true`
3. **Minimum Required Fields**: Test with only required fields

### Invalid Signup Tests
1. **Existing Email**: Use `<EMAIL>` (already exists)
2. **Password Mismatch**: Different `password` and `confirmPassword`
3. **Short Password**: Password less than 6 characters
4. **Terms Not Accepted**: `acceptTerms: false`
5. **Invalid Email**: Invalid email format

---

## 🔧 Frontend Testing

### Login Page Testing
1. Navigate to `/login`
2. Click on demo credentials to auto-fill
3. Test form validation
4. Test successful login flow
5. Test error handling

### Signup Page Testing
1. Navigate to `/signup`
2. Fill out registration form
3. Test form validation
4. Test successful signup flow
5. Test error handling

### Authentication State Testing
1. Test protected routes
2. Test token persistence
3. Test automatic logout on token expiry
4. Test refresh token functionality

---

## 📱 Browser Testing

### Local Storage Keys
After successful login, check browser's Local Storage for:
- `danfu_token`: JWT token
- `danfu_refresh_token`: Refresh token
- `danfu_user`: User data (JSON string)

### Session Testing
1. Login and refresh the page (should stay logged in)
2. Clear localStorage and refresh (should logout)
3. Test token expiry handling
4. Test automatic token refresh

---

## 🎯 Quick Test Commands

### Using Browser Console
```javascript
// Test login
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'admin123'
  })
});
console.log(await loginResponse.json());

// Check stored tokens
console.log('Token:', localStorage.getItem('danfu_token'));
console.log('User:', JSON.parse(localStorage.getItem('danfu_user')));
```

### Using cURL
```bash
# Login test
curl -X POST http://localhost:5174/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# Signup test
curl -X POST http://localhost:5174/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{"firstName":"Test","lastName":"User","email":"<EMAIL>","password":"test123","confirmPassword":"test123","acceptTerms":true}'
```

---

## 🚨 Error Codes & Messages

### Login Errors
- `User not found. Please check your email address.`
- `Invalid password. Please try again.`
- `Network error. Please try again.`

### Signup Errors
- `You must accept the terms and conditions to create an account.`
- `Passwords do not match. Please try again.`
- `Password must be at least 6 characters long.`
- `An account with this email already exists. Please try logging in instead.`

### Token Errors
- `Invalid refresh token.`
- `User not found.`
- `Token expired.`

---

## 🎉 Success Messages
- `Login successful! Welcome back.`
- `Account created successfully! Welcome to DANFU.`
- `Logged out successfully.`
- `Token refreshed successfully.`
