import { useState, useEffect, createContext, useContext } from 'react';
import { mockApi } from '../services/mockApi';
import { User, LoginCredentials, SignupData, AuthState } from '../types';

// Create Auth Context
const AuthContext = createContext<{
  authState: AuthState;
  login: (credentials: LoginCredentials) => Promise<{ success: boolean; error?: string }>;
  signup: (data: SignupData) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
} | null>(null);

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Auth hook for components
export const useAuthState = () => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    token: null,
    loading: true,
    error: null
  });

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = localStorage.getItem('danfu_token');
        const refreshTokenValue = localStorage.getItem('danfu_refresh_token');
        const userData = localStorage.getItem('danfu_user');

        if (token && userData) {
          const user = JSON.parse(userData);
          setAuthState({
            isAuthenticated: true,
            user,
            token,
            loading: false,
            error: null
          });
        } else if (refreshTokenValue) {
          // Try to refresh token
          const response = await mockApi.refreshToken(refreshTokenValue);
          if (response.success && response.user && response.token) {
            localStorage.setItem('danfu_token', response.token);
            localStorage.setItem('danfu_user', JSON.stringify(response.user));
            if (response.refreshToken) {
              localStorage.setItem('danfu_refresh_token', response.refreshToken);
            }
            
            setAuthState({
              isAuthenticated: true,
              user: response.user,
              token: response.token,
              loading: false,
              error: null
            });
          } else {
            // Clear invalid tokens
            localStorage.removeItem('danfu_token');
            localStorage.removeItem('danfu_refresh_token');
            localStorage.removeItem('danfu_user');
            setAuthState(prev => ({ ...prev, loading: false }));
          }
        } else {
          setAuthState(prev => ({ ...prev, loading: false }));
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        setAuthState(prev => ({ ...prev, loading: false, error: 'Failed to initialize authentication' }));
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials: LoginCredentials) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const response = await mockApi.login(credentials);
      
      if (response.success && response.user && response.token) {
        // Store auth data
        localStorage.setItem('danfu_token', response.token);
        localStorage.setItem('danfu_user', JSON.stringify(response.user));
        if (response.refreshToken) {
          localStorage.setItem('danfu_refresh_token', response.refreshToken);
        }
        
        setAuthState({
          isAuthenticated: true,
          user: response.user,
          token: response.token,
          loading: false,
          error: null
        });
        
        return { success: true };
      } else {
        setAuthState(prev => ({ 
          ...prev, 
          loading: false, 
          error: response.error || 'Login failed' 
        }));
        return { success: false, error: response.error };
      }
    } catch (error) {
      const errorMessage = 'Network error. Please try again.';
      setAuthState(prev => ({ ...prev, loading: false, error: errorMessage }));
      return { success: false, error: errorMessage };
    }
  };

  const signup = async (data: SignupData) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const response = await mockApi.signup(data);
      
      if (response.success && response.user && response.token) {
        // Store auth data
        localStorage.setItem('danfu_token', response.token);
        localStorage.setItem('danfu_user', JSON.stringify(response.user));
        if (response.refreshToken) {
          localStorage.setItem('danfu_refresh_token', response.refreshToken);
        }
        
        setAuthState({
          isAuthenticated: true,
          user: response.user,
          token: response.token,
          loading: false,
          error: null
        });
        
        return { success: true };
      } else {
        setAuthState(prev => ({ 
          ...prev, 
          loading: false, 
          error: response.error || 'Signup failed' 
        }));
        return { success: false, error: response.error };
      }
    } catch (error) {
      const errorMessage = 'Network error. Please try again.';
      setAuthState(prev => ({ ...prev, loading: false, error: errorMessage }));
      return { success: false, error: errorMessage };
    }
  };

  const logout = async () => {
    try {
      await mockApi.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear auth data
      localStorage.removeItem('danfu_token');
      localStorage.removeItem('danfu_refresh_token');
      localStorage.removeItem('danfu_user');
      
      setAuthState({
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: null
      });
    }
  };

  const refreshTokenFunc = async () => {
    const refreshTokenValue = localStorage.getItem('danfu_refresh_token');
    if (!refreshTokenValue) return;

    try {
      const response = await mockApi.refreshToken(refreshTokenValue);
      if (response.success && response.user && response.token) {
        localStorage.setItem('danfu_token', response.token);
        localStorage.setItem('danfu_user', JSON.stringify(response.user));
        if (response.refreshToken) {
          localStorage.setItem('danfu_refresh_token', response.refreshToken);
        }
        
        setAuthState(prev => ({
          ...prev,
          user: response.user,
          token: response.token
        }));
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      logout();
    }
  };

  return {
    authState,
    login,
    signup,
    logout,
    refreshToken: refreshTokenFunc
  };
};
