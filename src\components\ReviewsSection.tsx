import React, { useState, useEffect } from 'react';
import { <PERSON>, Quote, ChevronLeft, ChevronRight, Play, Pause, Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useReviews } from '../hooks/useApi';

const ReviewsSection = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const { data: reviews, loading, error } = useReviews();

  const staticReviews = [
    {
      id: 1,
      name: "<PERSON>",
      role: "Marathon Runner",
      rating: 5,
      comment: "The quality of DANFU products exceeded my expectations. The running shoes provided excellent support during my marathon training, and the customer service was outstanding.",
      product: "Pro Running Shoes",
      verified: true,
      avatar: "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100"
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "Fitness Trainer",
      rating: 5,
      comment: "Outstanding customer service and fast delivery. The compression wear fits perfectly and enhances my workout performance. I recommend DANFU to all my clients.",
      product: "Elite Compression Set",
      verified: true,
      avatar: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=100"
    },
    {
      id: 3,
      name: "David Rodriguez",
      role: "Professional Athlete",
      rating: 5,
      comment: "As a professional athlete, I trust DANFU for all my training equipment. The durability and performance are unmatched. Every product delivers exactly what it promises.",
      product: "Training Equipment Set",
      verified: true,
      avatar: "https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=100"
    },
    {
      id: 4,
      name: "Emma Williams",
      role: "Yoga Instructor",
      rating: 5,
      comment: "The yoga mat and accessories are incredible. Perfect grip, excellent durability, and the eco-friendly materials align with my values. Highly recommended!",
      product: "Premium Yoga Mat",
      verified: true,
      avatar: "https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=100"
    },
    {
      id: 5,
      name: "David Rodriguez",
      role: "Basketball Player",
      rating: 5,
      comment: "Amazing basketball shoes! The grip and support are unmatched. I've been using DANFU products for years and they never disappoint.",
      product: "Pro Basketball Shoes",
      verified: true,
      avatar: "https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=100"
    },
    {
      id: 6,
      name: "Lisa Thompson",
      role: "Swimming Coach",
      rating: 5,
      comment: "The swimwear quality is exceptional. Chlorine-resistant and comfortable for long training sessions. My entire team uses DANFU now.",
      product: "Professional Swimwear",
      verified: true,
      avatar: "https://images.pexels.com/photos/1239288/pexels-photo-1239288.jpeg?auto=compress&cs=tinysrgb&w=100"
    }
  ];

  const currentReviews = reviews || staticReviews;

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || !currentReviews.length) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % currentReviews.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, currentReviews.length]);

  const nextReview = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % currentReviews.length);
  };

  const prevReview = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + currentReviews.length) % currentReviews.length);
  };

  const toggleAutoPlay = () => {
    setIsAutoPlaying(!isAutoPlaying);
  };

  if (loading) {
    return (
      <section className="py-20" style={{ backgroundColor: '#2a2a2a' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin" style={{ color: '#ef4444' }} />
            <span className="ml-2" style={{ color: '#f8fafc' }}>Loading reviews...</span>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-20" style={{ backgroundColor: '#2a2a2a' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center" style={{ color: '#ef4444' }}>
            Error loading reviews: {error}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20" style={{ backgroundColor: '#2a2a2a' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <motion.h2
            className="text-4xl font-bold mb-4"
            style={{ color: '#f8fafc' }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            What Athletes Say About DANFU
          </motion.h2>
          <motion.p
            className="text-xl max-w-3xl mx-auto"
            style={{ color: '#94a3b8' }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Real feedback from real athletes who trust DANFU for their performance needs.
            Join thousands of satisfied customers worldwide.
          </motion.p>
        </div>

        {/* Carousel Controls */}
        <div className="flex justify-center items-center mb-8 space-x-4">
          <motion.button
            onClick={prevReview}
            className="p-3 text-white rounded-full transition-colors"
            style={{ backgroundColor: '#1a1a1a' }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#ef4444'}
            onMouseLeave={(e) => e.target.style.backgroundColor = '#1a1a1a'}
          >
            <ChevronLeft className="h-5 w-5" />
          </motion.button>

          <motion.button
            onClick={toggleAutoPlay}
            className="p-3 text-white rounded-full transition-colors"
            style={{ backgroundColor: '#1a1a1a' }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#ef4444'}
            onMouseLeave={(e) => e.target.style.backgroundColor = '#1a1a1a'}
          >
            {isAutoPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
          </motion.button>

          <motion.button
            onClick={nextReview}
            className="p-3 text-white rounded-full transition-colors"
            style={{ backgroundColor: '#1a1a1a' }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#ef4444'}
            onMouseLeave={(e) => e.target.style.backgroundColor = '#1a1a1a'}
          >
            <ChevronRight className="h-5 w-5" />
          </motion.button>
        </div>

        {/* Carousel Container */}
        <div className="relative max-w-4xl mx-auto">
          <div className="overflow-hidden">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, x: 300 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -300 }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
                className="grid grid-cols-1 md:grid-cols-2 gap-8"
              >
                {/* Current review */}
                {[currentReviews[currentIndex], currentReviews[(currentIndex + 1) % currentReviews.length]].map((review, index) => (
                  <motion.div
                    key={`${review.id}-${currentIndex}`}
                    className="border rounded-xl p-8 transition-all duration-300 group relative"
                    style={{
                      backgroundColor: '#1a1a1a',
                      borderColor: '#4a4a4a'
                    }}
                    onMouseEnter={(e) => e.currentTarget.style.borderColor = '#ef4444'}
                    onMouseLeave={(e) => e.currentTarget.style.borderColor = '#4a4a4a'}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.2 }}
                    whileHover={{ y: -5, scale: 1.02 }}
                  >
              <div className="absolute top-6 right-6 opacity-20 group-hover:opacity-30 transition-opacity">
                <Quote className="h-12 w-12" style={{ color: '#ef4444' }} />
              </div>
              
              <div className="flex items-center mb-6">
                <img 
                  src={review.avatar} 
                  alt={review.name}
                  className="w-16 h-16 rounded-full object-cover mr-4"
                />
                <div>
                  <h4 className="text-lg font-semibold" style={{ color: '#f8fafc' }}>{review.name}</h4>
                  <p className="text-sm font-medium" style={{ color: '#ef4444' }}>{review.role}</p>
                  <div className="flex items-center mt-1">
                    {[...Array(5)].map((_, i) => (
                      <Star 
                        key={i} 
                        className={`h-4 w-4 ${i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-600'}`} 
                      />
                    ))}
                  </div>
                </div>
              </div>
              
              <blockquote className="text-lg leading-relaxed mb-6" style={{ color: '#94a3b8' }}>
                "{review.comment}"
              </blockquote>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <span className="text-sm text-gray-400">Verified Purchase</span>
                  <span className="mx-2 text-gray-600">•</span>
                  <span className="text-sm text-red-500 font-medium">{review.product}</span>
                </div>
                {review.verified && (
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-xs text-green-500">Verified</span>
                  </div>
                )}
              </div>
                  </motion.div>
                ))}
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Carousel Indicators */}
          <div className="flex justify-center mt-8 space-x-2">
            {currentReviews.map((_, index) => (
              <motion.button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex ? 'bg-red-500' : 'bg-gray-600 hover:bg-gray-500'
                }`}
                whileHover={{ scale: 1.2 }}
                whileTap={{ scale: 0.9 }}
              />
            ))}
          </div>
        </div>

        <div className="text-center mt-12">
          <div className="bg-gray-900 border border-gray-800 rounded-xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">Join Our Satisfied Customers</h3>
            <p className="text-gray-400 mb-6">
              Experience the DANFU difference and see why athletes worldwide choose our products 
              for their training and competition needs.
            </p>
            <div className="flex items-center justify-center space-x-8 text-center">
              <div>
                <div className="text-3xl font-bold text-red-500">4.8</div>
                <div className="text-sm text-gray-400">Average Rating</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-red-500">10K+</div>
                <div className="text-sm text-gray-400">Reviews</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-red-500">98%</div>
                <div className="text-sm text-gray-400">Satisfaction</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ReviewsSection;