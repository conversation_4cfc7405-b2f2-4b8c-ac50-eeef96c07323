import { useState, useEffect } from 'react';
import { mockApi } from '../services/mockApi';
import { 
  Product, 
  Category, 
  Review, 
  BrandPartner, 
  SearchParams,
  PaginatedResponse,
  ApiResponse 
} from '../types';

// Generic hook for API calls
export function useApiCall<T>(
  apiCall: () => Promise<ApiResponse<T>>,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await apiCall();
        
        if (isMounted) {
          if (response.success) {
            setData(response.data);
          } else {
            setError(response.error || 'An error occurred');
          }
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err.message : 'An error occurred');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, dependencies);

  return { data, loading, error };
}

// Generic hook for paginated API calls
export function usePaginatedApiCall<T>(
  apiCall: (params?: SearchParams) => Promise<PaginatedResponse<T>>,
  initialParams?: SearchParams
) {
  const [data, setData] = useState<T[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [params, setParams] = useState<SearchParams>(initialParams || {});

  const fetchData = async (newParams?: SearchParams) => {
    try {
      setLoading(true);
      setError(null);
      
      const searchParams = newParams || params;
      const response = await apiCall(searchParams);
      
      if (response.success) {
        setData(response.data);
        setPagination(response.pagination);
      } else {
        setError('An error occurred while fetching data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const refetch = (newParams?: SearchParams) => {
    if (newParams) {
      setParams(newParams);
    }
    fetchData(newParams);
  };

  const nextPage = () => {
    if (pagination.page < pagination.totalPages) {
      const newParams = { ...params, page: pagination.page + 1 };
      refetch(newParams);
    }
  };

  const prevPage = () => {
    if (pagination.page > 1) {
      const newParams = { ...params, page: pagination.page - 1 };
      refetch(newParams);
    }
  };

  const goToPage = (page: number) => {
    if (page >= 1 && page <= pagination.totalPages) {
      const newParams = { ...params, page };
      refetch(newParams);
    }
  };

  return {
    data,
    pagination,
    loading,
    error,
    refetch,
    nextPage,
    prevPage,
    goToPage
  };
}

// Specific hooks for different data types
export function useProducts(params?: SearchParams) {
  return usePaginatedApiCall(mockApi.getProducts, params);
}

export function useProduct(id: number) {
  return useApiCall(() => mockApi.getProductById(id), [id]);
}

export function useCategories() {
  return useApiCall(mockApi.getCategories);
}

export function useReviews() {
  return useApiCall(mockApi.getReviews);
}

export function useBrandPartners() {
  return useApiCall(mockApi.getBrandPartners);
}

// Hook for search functionality
export function useProductSearch() {
  const [searchParams, setSearchParams] = useState<SearchParams>({});
  const { data, pagination, loading, error, refetch } = useProducts(searchParams);

  const search = (query: string) => {
    const newParams = { ...searchParams, query, page: 1 };
    setSearchParams(newParams);
    refetch(newParams);
  };

  const applyFilters = (filters: SearchParams['filters']) => {
    const newParams = { ...searchParams, filters, page: 1 };
    setSearchParams(newParams);
    refetch(newParams);
  };

  const clearFilters = () => {
    const newParams = { query: searchParams.query };
    setSearchParams(newParams);
    refetch(newParams);
  };

  return {
    products: data,
    pagination,
    loading,
    error,
    search,
    applyFilters,
    clearFilters,
    searchParams
  };
}
